package main

import (
	"cabycare-server/api"
	"cabycare-server/config"
	"cabycare-server/pkg/auth"
	"cabycare-server/pkg/cattoilet"
	"cabycare-server/pkg/notification"
	"cabycare-server/pkg/storage"
	"cabycare-server/pkg/video"
	"context"
	"log"
	"time"
)

// @title Video Server API
// @version 1.0
// @description 视频服务器和猫厕所 API 文档
// @BasePath /api
func main() {
	// 加载配置
	cfg, err := config.LoadConfig()
	if err != nil {
		log.Fatal(err)
	}

	// initialize notification service
	notificationService, err := notification.NewNotificationService(cfg)
	if err != nil {
		log.Fatalf("Failed to initialize notification service: %v", err)
	}
	// initialize storage service
	storageService, err := storage.NewStorageService(cfg)
	if err != nil {
		log.Fatalf("Failed to initialize storage service: %v", err)
	}

	// initialize video service
	cattoiletService, err := cattoilet.NewCatToiletService(cfg, notificationService, storageService)
	if err != nil {
		log.Fatalf("Failed to initialize cat toilet service: %v", err)
	}

	// 启动设备状态监控
	cattoiletService.StartDeviceStatusMonitor()

	// 启动OTA状态监控
	cattoiletService.StartOTAStatusMonitor()

	// 启动OTA缓存清理器（每天清理一次剩余时间小于2天的缓存）
	go func() {
		for {
			time.Sleep(24 * time.Hour) // 每天清理一次
			if err := cattoiletService.CleanSoonToExpireOTACache(); err != nil {
				log.Printf("清理即将过期的OTA缓存失败: %v", err)
			} else {
				log.Printf("成功清理即将过期的OTA缓存")
			}
		}
	}()

	cattoiletHandler := cattoilet.NewHandler(cattoiletService)
	videoHandler := video.NewHandler(storageService, cattoiletService)
	notificationHandler := notification.NewHandler(notificationService)

	// 启动免打扰通知处理器
	ctx := context.Background()
	notificationService.StartQuietNotificationProcessor(ctx)
	notificationService.StartWeeklyCleanup(ctx)

	// 创建 Logto 配置和服务
	logtoConfig := auth.NewLogtoConfig(
		cfg.Logto.Endpoint,
		cfg.Logto.AppID,
		cfg.Logto.AppSecret,
		cfg.Logto.APIResource,
		cfg.Logto.CallbackURI,
	)
	logtoService := auth.NewLogtoService(logtoConfig)
	authHandler := auth.NewAuthHandler(logtoService, cattoiletService)

	// 设置路由
	r := api.SetupRouter(cfg, videoHandler, cattoiletHandler, authHandler, cattoiletService, notificationHandler)

	// 启动服务器
	if err := r.Run(":5678"); err != nil {
		log.Fatal(err)
	}
}
