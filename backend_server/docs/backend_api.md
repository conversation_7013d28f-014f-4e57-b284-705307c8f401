# CabyCare Backend API 文档

## 基本信息

- **基础URL**: `http://your-server.com/api`
- **认证方式**: <PERSON><PERSON> (通过Logto认证)
- **数据格式**: JSON
- **编码**: UTF-8

## 认证相关

### 1. Logto回调
```
GET /api/callback
```

**说明**: Logto认证回调接口

### 2. 刷新Token
```
POST /api/refresh
```

**请求体:**
```json
{
  "refresh_token": "string"
}
```

**响应:**
```json
{
  "access_token": "string",
  "token_type": "Bearer",
  "expires_in": 3600,
  "refresh_token": "string"
}
```

### 3. 获取用户信息
```
GET /api/user/info
```

**请求头:**
```
Authorization: Bearer <token>
```

**响应:**
```json
{
  "user_id": "string",
  "username": "string",
  "email": "string",
  "nickname": "string"
}
```

### 4. 健康检查
```
GET /api/health
```

**响应:**
```json
{
  "status": "ok"
}
```

## 客户端管理

### 1. 注册客户端
```
POST /api/clients/register
```

**请求头:**
```
Authorization: Bearer <token>
```

**请求体:**
```json
{
  "user_id": "string",
  "client_id": "string",
  "client_type": "ios|android|web",
  "name": "string",
  "model": "string",
  "os_version": "string",
  "app_version": "string"
}
```

**响应:**
```json
{
  "id": 1,
  "user_id": "string",
  "client_id": "string",
  "client_type": "ios",
  "name": "iPhone 13",
  "model": "iPhone13,2",
  "os_version": "15.0",
  "app_version": "1.0.0",
  "last_active": "2024-01-01T00:00:00Z",
  "status": 1,
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z"
}
```

### 2. 更新客户端信息
```
PUT /api/clients/{client_id}
```

**请求头:**
```
Authorization: Bearer <token>
```

**请求体:**
```json
{
  "name": "string",
  "model": "string",
  "os_version": "string",
  "app_version": "string",
  "status": 1
}
```

### 3. 获取客户端信息
```
GET /api/clients/{client_id}
```

**请求头:**
```
Authorization: Bearer <token>
```

### 4. 获取用户客户端列表
```
GET /api/clients
```

**请求头:**
```
Authorization: Bearer <token>
```

### 5. 删除客户端
```
DELETE /api/clients/{client_id}
```

**请求头:**
```
Authorization: Bearer <token>
```

### 6. 注册推送Token
```
POST /api/clients/{client_id}/token
```

**请求头:**
```
Authorization: Bearer <token>
```

**请求体:**
```json
{
  "client_token": "string",
  "token_type": "apns|fcm",
  "is_sandbox": false
}
```

### 7. 删除推送Token
```
DELETE /api/clients/{client_id}/token
```

**请求头:**
```
Authorization: Bearer <token>
```

### 8. 获取推送Token信息
```
GET /api/clients/{client_id}/token
```

**请求头:**
```
Authorization: Bearer <token>
```

### 9. 客户端心跳
```
POST /api/clients/{client_id}/heartbeat
```

**请求头:**
```
Authorization: Bearer <token>
```

**请求体:**
```json
{
  "app_version": "string",
  "status": 1
}
```

### 10. 更新客户端状态
```
PUT /api/clients/{client_id}/status
```

**请求头:**
```
Authorization: Bearer <token>
```

**请求体:**
```json
{
  "status": 1,
  "app_version": "string"
}
```

## 猫咪管理

### 1. 创建猫咪
```
POST /api/cats
```

**请求头:**
```
Authorization: Bearer <token>
```

**请求体:**
```json
{
  "name": "string",
  "gender": 1,
  "birthday": "2020-01-01",
  "weight": 4.5,
  "breed": "string",
  "color": "string",
  "photos_base64": ["base64_string"],
  "avatar_base64": "base64_string"
}
```

**性别编码说明:**
- `0`: 未知
- `1`: 雄性/未知
- `-1`: 雌性/未知
- `10`: 雄性/已绝育
- `11`: 雄性/未绝育
- `-10`: 雌性/已绝育
- `-11`: 雌性/未绝育

**响应:**
```json
{
  "status": "success",
  "message": "Cat created successfully",
  "data": {
    "cat_id": "string",
    "user_id": "string",
    "name": "string",
    "gender": 1,
    "birthday": "2020-01-01T00:00:00Z",
    "breed": "string",
    "color": "string",
    "weight": 4.5,
    "avatar_url": "string",
    "status": 1,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 2. 获取猫咪信息
```
GET /api/cats/{cat_id}
```

**请求头:**
```
Authorization: Bearer <token>
```

### 3. 更新猫咪信息
```
PUT /api/cats/{cat_id}
```

**请求头:**
```
Authorization: Bearer <token>
```

**请求体:**
```json
{
  "name": "string",
  "gender": 1,
  "birthday": "2020-01-01",
  "weight": 4.5,
  "breed": "string",
  "color": "string",
  "status": 1,
  "avatar_base64": "base64_string"
}
```

### 4. 删除猫咪（软删除）
```
DELETE /api/cats/{cat_id}
```

**请求头:**
```
Authorization: Bearer <token>
```

### 5. 隐藏猫咪
```
PUT /api/cats/{cat_id}/hide
```

**请求头:**
```
Authorization: Bearer <token>
```

### 6. 恢复猫咪（取消隐藏）
```
PUT /api/cats/{cat_id}/restore
```

**请求头:**
```
Authorization: Bearer <token>
```

### 7. 获取用户猫咪列表（仅正常状态）
```
GET /api/cats
```

**请求头:**
```
Authorization: Bearer <token>
```

### 8. 获取用户隐藏的猫咪列表
```
GET /api/cats/hidden
```

**请求头:**
```
Authorization: Bearer <token>
```

### 9. 获取用户所有猫咪列表（包括隐藏，不包括已删除）
```
GET /api/cats/all
```

**请求头:**
```
Authorization: Bearer <token>
```

### 10. 获取猫咪每日统计
```
GET /api/cats/{cat_id}/metrics/daily?date=2024-01-01
```

**请求头:**
```
Authorization: Bearer <token>
```

### 11. 获取猫咪月度统计
```
GET /api/cats/{cat_id}/metrics/monthly?year=2024&month=1
```

**请求头:**
```
Authorization: Bearer <token>
```

### 12. 获取猫咪健康警报
```
GET /api/cats/{cat_id}/alerts?status=1
```

**请求头:**
```
Authorization: Bearer <token>
```

### 13. 标记猫咪状态
```
POST /api/cats/{cat_id}/state
```

**请求头:**
```
Authorization: Bearer <token>
```

**请求体:**
```json
{
  "state": "medical",
  "description": "Surgery recovery",
  "expected_duration_days": 14
}
```

**说明:**
- `state`: 状态类型，支持 `medical`（医疗）、`clothing`（穿衣）、`grooming`（美容）、`other`（其他）
- `description`: 状态描述（可选）
- `expected_duration_days`: 预期持续天数（可选，默认14天）

**响应:**
```json
{
  "success": true,
  "message": "Cat state marked successfully",
  "data": {
    "cat_id": "cat_123",
    "state": "medical",
    "expected_duration": 14
  }
}
```

### 14. 获取猫咪状态
```
GET /api/cats/{cat_id}/state
```

**请求头:**
```
Authorization: Bearer <token>
```

**响应（有状态）:**
```json
{
  "success": true,
  "data": {
    "cat_id": "cat_123",
    "has_state": true,
    "state": "medical",
    "description": "Surgery recovery",
    "start_time": "2024-01-01T10:00:00Z",
    "expected_end_time": "2024-01-15T10:00:00Z",
    "expected_duration": 14,
    "is_active": true
  }
}
```

**响应（无状态）:**
```json
{
  "success": true,
  "data": {
    "cat_id": "cat_123",
    "has_state": false
  }
}
```

### 15. 结束猫咪状态
```
DELETE /api/cats/{cat_id}/state
```

**请求头:**
```
Authorization: Bearer <token>
```

**响应:**
```json
{
  "success": true,
  "message": "Cat state ended successfully",
  "data": {
    "cat_id": "cat_123"
  }
}
```

## 用户管理

### 1. 根据用户名获取用户
```
GET /api/users/by-username?username=string
```

**请求头:**
```
Authorization: Bearer <token>
```

### 2. 根据ID获取用户
```
GET /api/users/{user_id}
```

**请求头:**
```
Authorization: Bearer <token>
```

### 3. 获取用户列表
```
GET /api/users
```

**请求头:**
```
Authorization: Bearer <token>
```

### 4. 获取用户设备列表
```
GET /api/users/{user_id}/devices
```

**请求头:**
```
Authorization: Bearer <token>
```

### 5. 获取用户资料
```
GET /api/users/{user_id}/profile
```

**请求头:**
```
Authorization: Bearer <token>
```

**响应:**
```json
{
  "user_id": "string",
  "nickname": "string",
  "real_name": "string",
  "gender": 0,
  "birthday": "1990-01-01T00:00:00Z",
  "avatar_url": "string",
  "address": "string",
  "city": "string",
  "country": "string",
  "emerg_contact": "string",
  "emerg_phone": "string",
  "is_verified": false,
  "updated_at": "2024-01-01T00:00:00Z"
}
```

### 6. 更新用户资料
```
PUT /api/users/{user_id}/profile
```

**请求头:**
```
Authorization: Bearer <token>
```

**请求体:**
```json
{
  "nickname": "string",
  "real_name": "string",
  "gender": 0,
  "birthday": "1990-01-01",
  "address": "string",
  "city": "string",
  "country": "string",
  "emerg_contact": "string",
  "emerg_phone": "string"
}
```

### 7. 获取用户设置
```
GET /api/users/{user_id}/settings
```

**请求头:**
```
Authorization: Bearer <token>
```

### 8. 更新用户设置
```
PUT /api/users/{user_id}/settings
```

**请求头:**
```
Authorization: Bearer <token>
```

**请求体:**
```json
{
  "language": "zh",
  "time_zone": "Asia/Shanghai",
  "notification": true,
  "newsletter": true,
  "theme": "light",
  "settings": "{...}"
}
```

### 9. 获取用户通知
```
GET /api/users/{user_id}/notifications
```

**请求头:**
```
Authorization: Bearer <token>
```

## 设备管理

### 1. 注册设备
```
POST /api/devices/register
```

**请求体:**
```json
{
  "device_id": "string",
  "user_id": "string",
  "hardware_sn": "string",
  "name": "string",
  "model": "string",
  "firmware_version": "string"
}
```

### 2. 设备心跳
```
POST /api/devices/heartbeat
```

**请求体:**
```json
{
  "device_id": "string",
  "signal_strength": 85,
  "ipv4": "*************",
  "ipv6": "fe80::1",
  "last_boot_time": "2024-01-01T00:00:00Z",
  "last_connect_time": "2024-01-01T00:00:00Z",
  "storage_usage": 45
}
```

### 3. 设备时区设置
```
POST /api/devices/timezone
```

**请求体:**
```json
{
  "device_id": "string",
  "timezone": "Asia/Shanghai"
}
```

### 4. 获取设备信息
```
GET /api/devices/{device_id}
```

### 5. 获取用户设备列表
```
GET /api/devices
```

**请求头:**
```
Authorization: Bearer <token>
```

### 6. 获取设备状态
```
GET /api/devices/{device_id}/status
```

### 7. 获取设备统计
```
GET /api/devices/{device_id}/statistics
```

### 8. 获取设备配置
```
GET /api/devices/{device_id}/config
```

### 9. 更新设备配置
```
PUT /api/devices/{device_id}/config
```

**请求体:**
```json
{
  "location": "string",
  "room_size": 25.5,
  "install_height": 1.2,
  "install_angle": 45.0,
  "camera_config": "{}",
  "weight_config": "{}",
  "accel_config": "{}",
  "env_sensor_config": "{}",
  "ai_config": "{}",
  "settings": "{}"
}
```

### 10. 获取设备OTA设置
```
GET /api/devices/{device_id}/setting
```

### 11. 更新设备OTA设置
```
PUT /api/devices/{device_id}/setting
```

**请求体:**
```json
{
  "auto_ota_upgrade": "on|off",
  "idle_update_start_hour": 2,
  "idle_update_end_hour": 4
}
```

### 12. 获取设备OTA状态
```
GET /api/devices/{device_id}/ota-status
```

### 13. 更新设备OTA状态
```
PUT /api/devices/{device_id}/ota-status
```

**请求体:**
```json
{
  "device_id": "string",
  "status": "idle|updating|failed|completed"
}
```

### 14. 创建用户硬件关联
```
POST /api/devices
```

**请求头:**
```
Authorization: Bearer <token>
```

**请求体:**
```json
{
  "user_id": "string",
  "hardware_sn": "string",
  "remark": "string"
}
```

### 15. 获取用户硬件列表
```
GET /api/devices/users/{user_id}
```

### 16. 获取硬件关联用户
```
GET /api/devices/hardware/{hardware_sn}
```

### 17. 检查用户硬件是否存在
```
GET /api/devices/check?user_id=string&hardware_sn=string
```

### 18. 获取用户可访问的设备
```
GET /api/devices/accessible
```

**请求头:**
```
Authorization: Bearer <token>
```

## 记录管理

### 1. 创建记录
```
POST /api/records
```

**请求体:**
```json
{
  "video_id": "string",
  "device_id": "string",
  "start_time": 1640995200,
  "end_time": 1640995260,
  "weight_litter": 2.5,
  "weight_cat": 4.2,
  "weight_waste": 0.3
}
```

### 2. 获取记录列表
```
GET /api/records?user_id=xxx&device_id=xxx&start_time=xxx&end_time=xxx
```

### 3. 获取设备记录
```
GET /api/records/device/{device_id}
```

### 4. 获取猫咪记录
```
GET /api/records/cat/{cat_id}
```

## 视频管理

### 1. 获取视频列表
```
GET /api/records/videos/list?path=string&start=2024-01-01&end=2024-01-01
```

**参数说明:**
- `path`: 设备路径
- `start`: 开始日期 (YYYY-MM-DD)
- `end`: 结束日期 (YYYY-MM-DD)

**响应:**
```json
[
  {
    "start": 1640995200,
    "duration": "60.0",
    "url": "string",
    "weight_litter": 2.5,
    "weight_cat": 4.2,
    "weight_waste": 0.3,
    "thumbnail_url": "string",
    "animal_id": "string"
  }
]
```

### 2. 获取播放列表
```
GET /api/records/videos/get?path=string&start=2024-01-01T00:00:00Z&duration=60
```

**响应**: m3u8格式的播放列表内容

### 3. 获取视频片段
```
GET /api/records/videos/{folder}/{filename}?bucket=string
```

**响应**: 视频片段二进制数据

### 4. 获取缩略图
```
GET /api/records/videos/thumbnail/{folder}?bucket=string
```

**响应**: JPEG格式的缩略图

### 5. 检查静态视频（服务间接口）
```
GET /api/records/videos/static/{video_id}
```

**请求头:**
```
Authorization: Service-Token <token>
```

## 家庭组管理

### 1. 创建家庭组
```
POST /api/family-groups
```

**请求头:**
```
Authorization: Bearer <token>
```

**请求体:**
```json
{
  "group_name": "string",
  "description": "string"
}
```

### 2. 获取家庭组列表
```
GET /api/family-groups
```

**请求头:**
```
Authorization: Bearer <token>
```

### 3. 获取家庭组详情
```
GET /api/family-groups/{group_id}
```

**请求头:**
```
Authorization: Bearer <token>
```

### 4. 更新家庭组信息
```
PUT /api/family-groups/{group_id}
```

**请求头:**
```
Authorization: Bearer <token>
```

**请求体:**
```json
{
  "group_name": "string",
  "description": "string"
}
```

### 5. 删除家庭组
```
DELETE /api/family-groups/{group_id}
```

**请求头:**
```
Authorization: Bearer <token>
```

### 6. 获取家庭组成员列表
```
GET /api/family-groups/{group_id}/members
```

**请求头:**
```
Authorization: Bearer <token>
```

### 7. 添加家庭组成员
```
POST /api/family-groups/{group_id}/members
```

**请求头:**
```
Authorization: Bearer <token>
```

**请求体:**
```json
{
  "user_id": "string",
  "nickname": "string",
  "role": 0
}
```

**角色说明:**
- `0`: 普通成员
- `1`: 管理员

### 8. 更新家庭组成员
```
PUT /api/family-groups/{group_id}/members/{member_id}
```

**请求头:**
```
Authorization: Bearer <token>
```

**请求体:**
```json
{
  "nickname": "string",
  "role": 0
}
```

### 9. 移除家庭组成员
```
DELETE /api/family-groups/{group_id}/members/{member_id}
```

**请求头:**
```
Authorization: Bearer <token>
```

### 10. 获取家庭组设备列表
```
GET /api/family-groups/{group_id}/devices
```

**请求头:**
```
Authorization: Bearer <token>
```

### 11. 添加设备到家庭组
```
POST /api/family-groups/{group_id}/devices
```

**请求头:**
```
Authorization: Bearer <token>
```

**请求体:**
```json
{
  "device_id": "string"
}
```

### 12. 从家庭组移除设备
```
DELETE /api/family-groups/{group_id}/devices/{device_id}
```

**请求头:**
```
Authorization: Bearer <token>
```

### 13. 发送家庭组邀请
```
POST /api/family-groups/{group_id}/invitations
```

**请求头:**
```
Authorization: Bearer <token>
```

**请求体:**
```json
{
  "invitee_id": "string",
  "role": 0,
  "expire_at": "2024-12-31T23:59:59Z"
}
```

### 14. 通过邮箱发送邀请
```
POST /api/family-groups/{group_id}/invitations/email
```

**请求头:**
```
Authorization: Bearer <token>
```

**请求体:**
```json
{
  "invitee_email": "string",
  "role": 0,
  "expire_at": "2024-12-31T23:59:59Z"
}
```

## 家庭组邀请管理

### 1. 获取收到的邀请
```
GET /api/family-groups/invitations/received
```

**请求头:**
```
Authorization: Bearer <token>
```

### 2. 获取发送的邀请
```
GET /api/family-groups/invitations/sent
```

**请求头:**
```
Authorization: Bearer <token>
```

### 3. 获取邀请详情
```
GET /api/family-groups/invitations/{invitation_id}
```

**请求头:**
```
Authorization: Bearer <token>
```

### 4. 处理邀请（接受/拒绝）
```
PUT /api/family-groups/invitations/{invitation_id}/process
```

**请求头:**
```
Authorization: Bearer <token>
```

**请求体:**
```json
{
  "accept": true
}
```

### 5. 取消邀请
```
DELETE /api/family-groups/invitations/{invitation_id}/cancel
```

**请求头:**
```
Authorization: Bearer <token>
```

## 通知管理

### 1. 获取通知设置
```
GET /api/notifications/settings
```

**请求头:**
```
Authorization: Bearer <token>
```

**响应:**
```json
{
  "user_id": "string",
  "enable_daily": true,
  "enable_stats": true,
  "quiet_hours_start": 22,
  "quiet_hours_end": 7,
  "timezone": "Asia/Shanghai",
  "updated_at": "2024-01-01T00:00:00Z"
}
```

### 2. 更新通知设置
```
PUT /api/notifications/settings
```

**请求头:**
```
Authorization: Bearer <token>
```

**请求体:**
```json
{
  "enable_daily": true,
  "enable_stats": true,
  "quiet_hours_start": 22,
  "quiet_hours_end": 7,
  "timezone": "Asia/Shanghai"
}
```

### 3. 创建通知
```
POST /api/notifications
```

**请求头:**
```
Authorization: Bearer <token>
```

**请求体:**
```json
{
  "type": "daily|abnormal|stats|health|maintain",
  "sub_type": "string",
  "title": "string",
  "body": "string",
  "priority": 1,
  "metadata": {}
}
```

### 4. 获取用户通知列表
```
GET /api/notifications?is_read=false&limit=20&offset=0
```

**请求头:**
```
Authorization: Bearer <token>
```

### 5. 标记通知为已读
```
PUT /api/notifications/{id}/read
```

**请求头:**
```
Authorization: Bearer <token>
```

## 统计相关

### 1. 获取猫咪每日统计
```
GET /api/metrics/cats/{cat_id}/daily?date=2024-01-01
```

**请求头:**
```
Authorization: Bearer <token>
```

### 2. 获取猫咪月度统计
```
GET /api/metrics/cats/{cat_id}/monthly?year=2024&month=1
```

**请求头:**
```
Authorization: Bearer <token>
```

## 存储资源访问

### 1. 获取资源文件
```
GET /api/storage/assets/{filepath}
```

**请求头:**
```
Authorization: Bearer <token>
```

**说明:**
- 主要用于获取猫咪头像等资源文件
- 所有存储资源都需要认证访问

## 猫咪状态说明

### 猫咪基础状态
- `status = 1`: 正常显示（默认状态）
- `status = 0`: 隐藏（不在常规列表中显示，但数据仍然存在）
- `status = -1`: 已删除（软删除，不在任何列表中显示）

### 猫咪特殊状态（用于特征保护）
- `medical`: 医疗状态（手术、治疗、康复等）
- `clothing`: 穿衣状态（术后服、保暖衣、防舔服等）
- `grooming`: 美容状态（洗澡、剃毛、美容等）
- `other`: 其他特殊状态

**说明:**
- 特殊状态用于通知AI系统暂停特征演化，避免在猫咪外观临时改变期间删除重要特征
- 状态会自动过期，也可以手动结束
- 同一时间一只猫咪只能有一个活跃的特殊状态

## 设备状态说明

- `status = 1`: 正常
- `status = 0`: 离线
- `status = -1`: 已删除

## 客户端状态说明

- `status = 1`: 正常
- `status = 2`: 禁用

## 家庭组角色说明

- `role = 0`: 普通成员
- `role = 1`: 管理员

## 家庭组邀请状态说明

- `status = 0`: 待处理
- `status = 1`: 已接受
- `status = 2`: 已拒绝
- `status = 3`: 已过期

## 通知类型说明

### 通知类型 (type)
- `daily`: 日常通知
- `abnormal`: 异常通知
- `stats`: 统计通知
- `health`: 健康通知
- `maintain`: 维护通知

### 通知优先级 (priority)
- `1`: 低优先级
- `2`: 普通优先级
- `3`: 高优先级
- `4`: 紧急

## 错误响应格式

```json
{
  "error": "error_message",
  "code": 400
}
```

## 常见HTTP状态码

- `200`: 成功
- `201`: 创建成功
- `400`: 请求参数错误
- `401`: 未授权
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

## 注意事项

1. 所有需要认证的接口都必须在请求头中包含 `Authorization: Bearer <token>`
2. 时间格式统一使用 RFC3339 格式: `2006-01-02T15:04:05Z07:00`
3. 猫咪删除使用软删除机制，数据仍保留在数据库中
4. 隐藏功能适用于猫咪暂时离开或不想在常规列表中显示的场景
5. 所有涉及用户数据的操作都会验证用户权限
6. 存储资源（如头像）都需要认证访问，无公开访问权限
7. 家庭组功能允许用户共享设备访问权限
8. 通知系统支持免打扰时间设置
9. 设备支持OTA升级管理
10. 客户端需要定期发送心跳保持活跃状态
11. **猫咪状态管理**：用于通知AI系统猫咪处于特殊状态，避免在外观临时改变期间进行特征演化
12. **状态自动过期**：猫咪特殊状态会根据设定的预期时间自动过期，也可以手动结束
13. **状态互斥**：同一时间一只猫咪只能有一个活跃的特殊状态，新状态会自动结束旧状态