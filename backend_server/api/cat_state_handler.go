package api

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

// CatStateHandler 猫咪状态处理器
type CatStateHandler struct {
	stateService CatStateService
}

// CatStateService 猫咪状态服务接口
type CatStateService interface {
	MarkCatState(userID, catID, state, description string, expectedDuration int) error
	EndCatState(userID, catID string) error
	GetActiveCatState(userID, catID string) (*CatStateInfo, error)
}

// CatStateInfo 猫咪状态信息
type CatStateInfo struct {
	UserID           string    `json:"user_id"`
	CatID            string    `json:"cat_id"`
	State            string    `json:"state"`
	Description      string    `json:"description"`
	StartTime        time.Time `json:"start_time"`
	ExpectedEndTime  time.Time `json:"expected_end_time"`
	ExpectedDuration int       `json:"expected_duration_days"`
	IsActive         bool      `json:"is_active"`
}

// NewCatStateHandler 创建猫咪状态处理器
func NewCatStateHandler(service CatStateService) *CatStateHandler {
	return &CatStateHandler{
		stateService: service,
	}
}

// MarkCatStateRequest 标记猫咪状态请求
type MarkCatStateRequest struct {
	State            string `json:"state" binding:"required"`
	Description      string `json:"description"`
	ExpectedDuration int    `json:"expected_duration_days"`
}

// MarkCatState 标记猫咪状态
// @Summary 标记猫咪状态
// @Description 标记猫咪处于特殊状态（医疗、穿衣等）
// @Tags cats
// @Accept json
// @Produce json
// @Param cat_id path string true "猫咪ID"
// @Param request body MarkCatStateRequest true "状态信息"
// @Success 200 {object} Response
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/cats/{cat_id}/state [post]
func (h *CatStateHandler) MarkCatState(c *gin.Context) {
	var req MarkCatStateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request: " + err.Error()})
		return
	}

	catID := c.Param("cat_id")
	if catID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Cat ID is required"})
		return
	}

	// 从认证上下文获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// 验证状态类型
	if !isValidStateType(req.State) {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid state type. Must be one of: medical, clothing, grooming, other"})
		return
	}

	// 设置默认值
	if req.ExpectedDuration <= 0 {
		req.ExpectedDuration = 14 // 默认两周
	}

	// 调用服务标记状态
	err := h.stateService.MarkCatState(userID.(string), catID, req.State, req.Description, req.ExpectedDuration)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to mark cat state: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Cat state marked successfully",
		"data": gin.H{
			"cat_id":            catID,
			"state":             req.State,
			"expected_duration": req.ExpectedDuration,
		},
	})
}

// EndCatState 结束猫咪状态
// @Summary 结束猫咪状态
// @Description 标记猫咪特殊状态结束
// @Tags cats
// @Accept json
// @Produce json
// @Param cat_id path string true "猫咪ID"
// @Success 200 {object} Response
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/cats/{cat_id}/state [delete]
func (h *CatStateHandler) EndCatState(c *gin.Context) {
	catID := c.Param("cat_id")
	if catID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Cat ID is required"})
		return
	}

	// 从认证上下文获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// 调用服务结束状态
	err := h.stateService.EndCatState(userID.(string), catID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to end cat state: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Cat state ended successfully",
		"data": gin.H{
			"cat_id": catID,
		},
	})
}

// GetCatState 获取猫咪状态
// @Summary 获取猫咪状态
// @Description 获取猫咪当前状态信息
// @Tags cats
// @Accept json
// @Produce json
// @Param cat_id path string true "猫咪ID"
// @Success 200 {object} Response
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/cats/{cat_id}/state [get]
func (h *CatStateHandler) GetCatState(c *gin.Context) {
	catID := c.Param("cat_id")
	if catID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Cat ID is required"})
		return
	}

	// 从认证上下文获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// 调用服务获取状态
	stateInfo, err := h.stateService.GetActiveCatState(userID.(string), catID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get cat state: " + err.Error()})
		return
	}

	if stateInfo == nil {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"data": gin.H{
				"cat_id":    catID,
				"has_state": false,
			},
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"cat_id":             catID,
			"has_state":          true,
			"state":              stateInfo.State,
			"description":        stateInfo.Description,
			"start_time":         stateInfo.StartTime,
			"expected_end_time":  stateInfo.ExpectedEndTime,
			"expected_duration":  stateInfo.ExpectedDuration,
			"is_active":          stateInfo.IsActive,
		},
	})
}

// 辅助函数
func isValidStateType(state string) bool {
	validStates := map[string]bool{
		"medical":  true,
		"clothing": true,
		"grooming": true,
		"other":    true,
	}
	return validStates[state]
}
