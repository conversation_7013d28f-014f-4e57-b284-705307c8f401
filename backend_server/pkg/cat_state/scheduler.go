package cat_state

import (
	"log"
	"time"
)

// Scheduler 状态清理调度器
type Scheduler struct {
	service  *Service
	interval time.Duration
	stopChan chan bool
}

// NewScheduler 创建调度器
func NewScheduler(service *Service, interval time.Duration) *Scheduler {
	return &Scheduler{
		service:  service,
		interval: interval,
		stopChan: make(chan bool),
	}
}

// Start 启动调度器
func (s *Scheduler) Start() {
	log.Printf("Starting cat state cleanup scheduler with interval: %v", s.interval)
	
	ticker := time.NewTicker(s.interval)
	defer ticker.Stop()

	// 立即执行一次清理
	s.cleanup()

	for {
		select {
		case <-ticker.C:
			s.cleanup()
		case <-s.stopChan:
			log.Println("Cat state cleanup scheduler stopped")
			return
		}
	}
}

// Stop 停止调度器
func (s *Scheduler) Stop() {
	s.stopChan <- true
}

// cleanup 执行清理
func (s *Scheduler) cleanup() {
	log.Println("Running cat state cleanup...")
	
	err := s.service.AutoCleanupExpiredStates()
	if err != nil {
		log.Printf("Cat state cleanup failed: %v", err)
	} else {
		log.Println("Cat state cleanup completed")
	}
}
