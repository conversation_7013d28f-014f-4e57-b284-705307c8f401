package cat_state

import (
	"database/sql"
	"fmt"
	"log"
	"time"

	"cabycare-server/api"
)

// Service 猫咪状态服务
type Service struct {
	db *sql.DB
}

// NewService 创建猫咪状态服务
func NewService(db *sql.DB) *Service {
	return &Service{db: db}
}

// MarkCatState 标记猫咪状态
func (s *Service) MarkCatState(userID, catID, state, description string, expectedDuration int) error {
	// 1. 先结束任何现有的活跃状态
	err := s.endActiveState(userID, catID)
	if err != nil {
		log.Printf("Warning: failed to end existing active state: %v", err)
	}

	// 2. 创建新的状态记录
	startTime := time.Now()
	expectedEndTime := startTime.Add(time.Duration(expectedDuration) * 24 * time.Hour)

	query := `
		INSERT INTO cat_states (
			user_id, cat_id, state, description, 
			start_time, expected_end_time, expected_duration_days,
			is_active, created_at, updated_at
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	_, err = s.db.Exec(query,
		userID, catID, state, description,
		startTime, expectedEndTime, expectedDuration,
		true, startTime, startTime,
	)

	if err != nil {
		return fmt.Errorf("failed to insert cat state: %w", err)
	}

	log.Printf("Marked cat %s (user %s) as %s for %d days", catID, userID, state, expectedDuration)
	return nil
}

// EndCatState 结束猫咪状态
func (s *Service) EndCatState(userID, catID string) error {
	return s.endActiveState(userID, catID)
}

// GetActiveCatState 获取活跃的猫咪状态
func (s *Service) GetActiveCatState(userID, catID string) (*api.CatStateInfo, error) {
	query := `
		SELECT user_id, cat_id, state, description, 
			   start_time, expected_end_time, expected_duration_days,
			   is_active
		FROM cat_states 
		WHERE user_id = ? AND cat_id = ? AND is_active = true
		ORDER BY created_at DESC 
		LIMIT 1
	`

	var stateInfo api.CatStateInfo
	err := s.db.QueryRow(query, userID, catID).Scan(
		&stateInfo.UserID,
		&stateInfo.CatID,
		&stateInfo.State,
		&stateInfo.Description,
		&stateInfo.StartTime,
		&stateInfo.ExpectedEndTime,
		&stateInfo.ExpectedDuration,
		&stateInfo.IsActive,
	)

	if err == sql.ErrNoRows {
		return nil, nil // 没有活跃状态
	}

	if err != nil {
		return nil, fmt.Errorf("failed to query cat state: %w", err)
	}

	return &stateInfo, nil
}

// endActiveState 结束活跃状态
func (s *Service) endActiveState(userID, catID string) error {
	query := `
		UPDATE cat_states 
		SET is_active = false, end_time = ?, updated_at = ?
		WHERE user_id = ? AND cat_id = ? AND is_active = true
	`

	now := time.Now()
	result, err := s.db.Exec(query, now, now, userID, catID)
	if err != nil {
		return fmt.Errorf("failed to end active state: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected > 0 {
		log.Printf("Ended active state for cat %s (user %s)", catID, userID)
	}

	return nil
}

// GetExpiredStates 获取过期的状态（用于自动清理）
func (s *Service) GetExpiredStates() ([]api.CatStateInfo, error) {
	query := `
		SELECT user_id, cat_id, state, description, 
			   start_time, expected_end_time, expected_duration_days,
			   is_active
		FROM cat_states 
		WHERE is_active = true AND expected_end_time < ?
	`

	rows, err := s.db.Query(query, time.Now())
	if err != nil {
		return nil, fmt.Errorf("failed to query expired states: %w", err)
	}
	defer rows.Close()

	var expiredStates []api.CatStateInfo
	for rows.Next() {
		var state api.CatStateInfo
		err := rows.Scan(
			&state.UserID,
			&state.CatID,
			&state.State,
			&state.Description,
			&state.StartTime,
			&state.ExpectedEndTime,
			&state.ExpectedDuration,
			&state.IsActive,
		)
		if err != nil {
			log.Printf("Error scanning expired state: %v", err)
			continue
		}
		expiredStates = append(expiredStates, state)
	}

	return expiredStates, nil
}

// AutoCleanupExpiredStates 自动清理过期状态
func (s *Service) AutoCleanupExpiredStates() error {
	expiredStates, err := s.GetExpiredStates()
	if err != nil {
		return fmt.Errorf("failed to get expired states: %w", err)
	}

	for _, state := range expiredStates {
		err := s.EndCatState(state.UserID, state.CatID)
		if err != nil {
			log.Printf("Failed to auto-end expired state for cat %s: %v", state.CatID, err)
		} else {
			log.Printf("Auto-ended expired state for cat %s (user %s)", state.CatID, state.UserID)
		}
	}

	return nil
}

// CreateTable 创建数据表
func (s *Service) CreateTable() error {
	query := `
		CREATE TABLE IF NOT EXISTS cat_states (
			id INT AUTO_INCREMENT PRIMARY KEY,
			user_id VARCHAR(255) NOT NULL,
			cat_id VARCHAR(255) NOT NULL,
			state VARCHAR(50) NOT NULL,
			description TEXT,
			start_time DATETIME NOT NULL,
			end_time DATETIME NULL,
			expected_end_time DATETIME NOT NULL,
			expected_duration_days INT NOT NULL,
			is_active BOOLEAN NOT NULL DEFAULT true,
			created_at DATETIME NOT NULL,
			updated_at DATETIME NOT NULL,
			
			INDEX idx_user_cat_active (user_id, cat_id, is_active),
			INDEX idx_expected_end_time (expected_end_time),
			INDEX idx_is_active (is_active)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
	`

	_, err := s.db.Exec(query)
	if err != nil {
		return fmt.Errorf("failed to create cat_states table: %w", err)
	}

	log.Println("Cat states table created or already exists")
	return nil
}
