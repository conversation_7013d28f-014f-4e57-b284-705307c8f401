package cattoilet

import (
	"cabycare-server/pkg/algo"
	"cabycare-server/pkg/cat"
	"context"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
	"gorm.io/gorm"
)

type Handler struct {
	service *CatToiletService
}

func NewHandler(service *CatToiletService) *Handler {
	return &Handler{service: service}
}

// Register godoc
// @Summary 注册新用户
// @Description 创建新用户账号
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param user body UserRegisterRequest true "用户注册信息"
// @Success 200 {object} User "注册成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/users/register [post]
func (h *Handler) Register(c *gin.Context) {
	var req struct {
		Username string `json:"username"`
		Password string `json:"password"`
		Email    string `json:"email"`
		Phone    string `json:"phone"`
		Nickname string `json:"nickname"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 创建用户对象
	user := &User{
		Username:     req.Username,
		PasswordHash: req.Password, // 这里传入明文密码，在service层会进行加密
		Email:        req.Email,
		Phone:        req.Phone,
		Nickname:     req.Nickname,
	}

	if err := h.service.CreateUser(user); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 清除敏感信息
	user.PasswordHash = ""
	c.JSON(http.StatusOK, user)
}

// Login godoc
// @Summary 用户登录
// @Description 用户登录并返回token
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param user body LoginRequest true "登录信息"
// @Success 200 {object} LoginResponse "登录成功"
// @Failure 401 {object} ErrorResponse "登录失败"
// @Router /api/users/login [post]
func (h *Handler) Login(c *gin.Context) {
	var req struct {
		Username string `json:"username"`
		Password string `json:"password"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	user, err := h.service.ValidateUser(req.Username, req.Password)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid credentials"})
		return
	}

	// Ensure notification settings exist for the user
	if err := h.service.EnsureNotificationSettings(user.UserID); err != nil {
		// Log the error but don't fail the login
		fmt.Printf("Failed to ensure notification settings: %v\n", err)
	}

	user.PasswordHash = "" // 清除密码后返回
	c.JSON(http.StatusOK, user)
}

// 猫咪相关 API
// CreateCat godoc
// @Summary 创建猫咪
// @Description 创建新的猫咪档案，支持照片上传和AI分析
// @Tags 猫咪管理
// @Accept json
// @Produce json
// @Param cat body CreateCatRequest true "猫咪信息"
// @Success 201 {object} CreateCatResponse "创建成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 401 {object} ErrorResponse "未授权"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/cats [post]
func (h *Handler) CreateCat(c *gin.Context) {
	// 获取用户ID（通过认证中间件设置）
	userID := c.GetString("user_id")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
		return
	}

	var req CreateCatRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("Invalid request format: %v", err)})
		return
	}

	// 验证gender编码
	validGenders := []int8{0, 1, -1, 10, 11, -10, -11}
	isValidGender := false
	for _, validGender := range validGenders {
		if req.Gender == validGender {
			isValidGender = true
			break
		}
	}
	if !isValidGender {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid gender code. Valid codes: 0(unknown), 1(male/unknown), -1(female/unknown), 10(male/neutered), 11(male/not neutered), -10(female/neutered), -11(female/not neutered)",
		})
		return
	}

	// 使用雪花算法生成猫咪ID
	catID := algo.GenerateCatID(userID)

	// 解析生日（如果提供）
	var birthday time.Time
	if req.Birthday != nil && *req.Birthday != "" {
		parsedBirthday, err := time.Parse("2006-01-02", *req.Birthday)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid birthday format. Use YYYY-MM-DD"})
			return
		}
		birthday = parsedBirthday
	} else {
		// 如果没有提供生日，设置为当前日期（表示刚刚领养或不确定）
		birthday = time.Now()
	}

	var weight float64
	if req.Weight != nil && *req.Weight > 0 {
		weight = *req.Weight
	}

	// 创建猫咪对象
	newCat := &cat.Cat{
		CatID:     catID,
		UserID:    userID,
		Name:      req.Name,
		Gender:    req.Gender,
		Birthday:  birthday,
		Weight:    weight,
		Status:    1, // 1 = 活跃状态
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// 设置可选字段
	if req.Breed != nil {
		newCat.Breed = *req.Breed
	} else {
		newCat.Breed = "TBD" // 待AI分析确定
	}

	if req.Color != nil {
		newCat.Color = *req.Color
	} else {
		newCat.Color = "TBD" // 待AI分析确定
	}

	// 创建猫咪记录
	if err := h.service.CreateCat(newCat); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to create cat: %v", err)})
		return
	}

	// 处理头像上传 - 优先使用专门的头像字段
	var avatarUploaded bool
	if req.AvatarBase64 != nil && *req.AvatarBase64 != "" {
		// 上传专门的头像
		avatarURL, err := h.service.UploadCatAvatar(newCat.CatID, *req.AvatarBase64)
		if err != nil {
			// 头像上传失败不影响猫咪创建，只记录错误
			fmt.Printf("Failed to upload cat avatar: %v\n", err)
		} else {
			// 更新猫咪的头像URL
			newCat.AvatarURL = avatarURL
			if err := h.service.UpdateCat(newCat); err != nil {
				fmt.Printf("Failed to update cat avatar URL: %v\n", err)
			}
			avatarUploaded = true
		}
	}

	// 如果没有专门的头像且提供了照片，处理第一张照片作为头像
	if !avatarUploaded && len(req.PhotosBase64) > 0 {
		// 存储第一张照片作为头像
		avatarURL, err := h.service.UploadCatAvatar(newCat.CatID, req.PhotosBase64[0])
		if err != nil {
			// 头像上传失败不影响猫咪创建，只记录错误
			fmt.Printf("Failed to upload cat avatar: %v\n", err)
		} else {
			// 更新猫咪的头像URL
			newCat.AvatarURL = avatarURL
			if err := h.service.UpdateCat(newCat); err != nil {
				fmt.Printf("Failed to update cat avatar URL: %v\n", err)
			}
		}

		// TODO: 如果提供了照片，异步调用CabyAI进行分析
		// go h.analyzePhotosAsync(catID, req.PhotosBase64)
	}

	c.JSON(http.StatusCreated, CreateCatResponse{
		Status:  "success",
		Message: "Cat created successfully",
		Data:    newCat,
	})
}

// GetCat godoc
// @Summary 获取猫咪信息
// @Description 根据猫咪ID获取详细信息
// @Tags 猫咪管理
// @Accept json
// @Produce json
// @Param cat_id path string true "猫咪ID"
// @Success 200 {object} cat.Cat "获取成功"
// @Failure 404 {object} ErrorResponse "猫咪不存在"
// @Router /api/cats/{cat_id} [get]
func (h *Handler) GetCat(c *gin.Context) {
	catID := c.Param("cat_id")

	// 获取用户ID（通过认证中间件设置）
	userID := c.GetString("user_id")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
		return
	}

	cat, err := h.service.GetCat(catID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Cat not found"})
		return
	}

	// 验证用户权限：只能获取自己的猫咪信息
	if cat.UserID != userID {
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied: you can only access your own cats"})
		return
	}

	// 处理头像URL
	h.service.ProcessCatAvatarURL(cat)

	c.JSON(http.StatusOK, cat)
}

// ListUserCats godoc
// @Summary 获取用户的猫咪列表
// @Description 获取当前认证用户的所有猫咪
// @Tags 猫咪管理
// @Accept json
// @Produce json
// @Success 200 {array} cat.Cat "获取成功"
// @Failure 401 {object} ErrorResponse "未授权"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/cats [get]
func (h *Handler) ListUserCats(c *gin.Context) {
	// 获取用户ID（通过认证中间件设置）
	userID := c.GetString("user_id")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
		return
	}

	cats, err := h.service.ListUserCats(userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to get user cats: %v", err)})
		return
	}

	// 为每个猫咪处理头像URL
	for i := range cats {
		h.service.ProcessCatAvatarURL(&cats[i])
	}

	c.JSON(http.StatusOK, cats)
}

// 视频记录相关 API
// CreateShitRecord godoc
// @Summary 创建视频记录
// @Description 创建新的视频记录信息
// @Tags 视频记录
// @Accept json
// @Produce json
// @Param record body ApiRecordShit true "视频记录信息"
// @Success 200 {object} ApiRecordShit "创建成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Router /api/records [post]
func (h *Handler) CreateShitRecord(c *gin.Context) {
	var apiRecord ApiRecordShit
	if err := c.ShouldBindJSON(&apiRecord); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 验证必填字段
	if apiRecord.Record.DeviceID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "device_id is required"})
		return
	}

	// 如果请求中包含时间戳格式的时间，需要转换为Unix时间戳
	if apiRecord.Record.StartTime == 0 {
		// 如果没有设置StartTime，或者是0（这意味着客户端可能提供了别的格式），
		// 则使用当前时间
		apiRecord.Record.StartTime = time.Now().Unix()
	}

	if err := h.service.CreateShitRecord(&apiRecord.Record, apiRecord.UserId); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"video_id": apiRecord.Record.VideoID,
		"status":   "created",
	})
}

// ListRecords godoc
// @Summary 获取视频记录列表
// @Description 根据条件获取视频记录列表
// @Tags 视频记录
// @Accept json
// @Produce json
// @Param user_id query string false "用户ID"
// @Param device_id query string false "设备ID"
// @Param cat_id query string false "猫咪ID"
// @Param start_time query string false "开始时间 (RFC3339格式)"
// @Param end_time query string false "结束时间 (RFC3339格式)"
// @Success 200 {array} RecordShit "获取成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Router /api/records [get]
func (h *Handler) ListRecords(c *gin.Context) {
	userID := c.Query("user_id")
	deviceID := c.Query("device_id")
	catID := c.Query("cat_id")

	// 解析时间范围
	var startTime, endTime *time.Time
	if startStr := c.Query("start_time"); startStr != "" {
		t, err := time.Parse(time.RFC3339, startStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid start_time format"})
			return
		}
		startTime = &t
	}
	if endStr := c.Query("end_time"); endStr != "" {
		t, err := time.Parse(time.RFC3339, endStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid end_time format"})
			return
		}
		endTime = &t
	}

	var records []RecordShit
	var err error

	if deviceID != "" {
		records, err = h.service.ListDeviceRecords(deviceID, startTime, endTime)
	} else if catID != "" {
		records, err = h.service.ListCatRecords(catID)
	} else if userID != "" {
		records, err = h.service.ListUserRecords(userID, startTime, endTime)
	} else {
		c.JSON(http.StatusBadRequest, gin.H{"error": "must specify user_id, device_id or cat_id"})
		return
	}

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 为API响应处理记录 - 添加RFC3339格式的时间字符串以供客户端使用
	type RecordResponse struct {
		RecordShit
		StartTimeISO string  `json:"start_time_iso"`
		EndTimeISO   *string `json:"end_time_iso,omitempty"`
	}

	var responseRecords []RecordResponse
	for _, record := range records {
		startTimeISO := time.Unix(record.StartTime, 0).Format(time.RFC3339)
		var endTimeISO *string
		if record.EndTime != nil {
			tmp := time.Unix(*record.EndTime, 0).Format(time.RFC3339)
			endTimeISO = &tmp
		}
		responseRecords = append(responseRecords, RecordResponse{
			RecordShit:   record,
			StartTimeISO: startTimeISO,
			EndTimeISO:   endTimeISO,
		})
	}

	c.JSON(http.StatusOK, responseRecords)
}

// 设备相关 API
// RegisterDevice godoc
// @Summary 注册新设备
// @Description 注册新的智能猫厕所设备
// @Tags 设备管理
// @Accept json
// @Produce json
// @Param device body Device true "设备信息"
// @Success 200 {object} Device "注册成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Router /api/devices [post]
func (h *Handler) RegisterDevice(c *gin.Context) {
	var device Device
	if err := c.ShouldBindJSON(&device); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := h.service.CreateDevice(&device); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, device)
}

// DeviceHeartbeat godoc
// @Summary 设备心跳
// @Description 更新设备在线状态和相关信息，并返回自动OTA更新设置和版本信息
// @Tags 设备管理
// @Accept multipart/form-data
// @Produce json
// @Param device_id formData string true "设备ID"
// @Param signal_strength formData int8 false "信号强度百分比 (0-100)"
// @Param ipv4 formData string false "IPv4 地址"
// @Param ipv6 formData string false "IPv6 地址"
// @Param storage_usage formData int8 false "存储使用率百分比 (0-100)"
// @Param firmware_version formData string false "固件版本号"
// @Success 200 {object} map[string]interface{} "心跳成功，包含OTA相关信息"
// @Failure 400 {object} ErrorResponse "设备ID不能为空"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/devices/heartbeat [post]
func (h *Handler) DeviceHeartbeat(c *gin.Context) {
	deviceID := c.PostForm("device_id")
	if deviceID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "device_id is required"})
		return
	}

	// 获取可选参数
	signalStrength := int8(0)
	if signalStr := c.PostForm("signal_strength"); signalStr != "" {
		signalInt, err := strconv.Atoi(signalStr)
		if err == nil {
			signalStrength = int8(signalInt)
		}
	}

	storageUsage := int8(0)
	if storageStr := c.PostForm("storage_usage"); storageStr != "" {
		storageInt, err := strconv.Atoi(storageStr)
		if err == nil {
			storageUsage = int8(storageInt)
		}
	}

	// 获取固件版本参数
	firmwareVersion := c.PostForm("firmware_version")

	// 创建状态请求
	req := &DeviceStatusRequest{
		DeviceID:        deviceID,
		SignalStrength:  signalStrength,
		IPv4:            c.PostForm("ipv4"),
		IPv6:            c.PostForm("ipv6"),
		StorageUsage:    storageUsage,
		LastBootTime:    nil, // 这些字段暂时不处理
		LastConnectTime: nil,
	}

	// 处理心跳请求
	if err := h.service.UpdateDeviceStatus(req); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 如果提供了固件版本，则单独更新设备的固件版本
	if firmwareVersion != "" {
		if err := h.service.UpdateDeviceFirmwareVersion(deviceID, firmwareVersion); err != nil {
			// 记录警告但不影响主要的心跳响应
			fmt.Printf("Warning: failed to update device firmware version: %v\n", err)
		}
	}

	// 获取设备自动OTA更新设置
	deviceSettings, err := h.service.GetDeviceAutoOTASettings(deviceID)
	if err != nil {
		// 如果获取失败，默认为关闭状态
		c.JSON(http.StatusOK, gin.H{
			"status":                 "ok",
			"auto_ota_upgrade":       "off",
			"idle_update_start_hour": int8(2), // 默认闲时更新开始时间：凌晨2点
			"idle_update_end_hour":   int8(4), // 默认闲时更新结束时间：凌晨4点
			"ota_version":            "",
			"ota_required":           true,
			"ota_url":                "",
			"ota_md5_url":            "",
		})
		return
	}

	// 获取OTA版本信息
	otaVersion, err := h.getOTAVersionFromMinio()
	if err != nil {
		fmt.Printf("Failed to get OTA version: %v\n", err)
		otaVersion = ""
	}

	// 生成OTA下载链接 - 只有当自动OTA升级开启且有版本信息时才生成
	otaURL := ""
	otaMD5URL := ""
	if deviceSettings.AutoOTAUpgrade == "on" && otaVersion != "" {
		otaURLs, err := h.getCachedOTADownloadURL(otaVersion)
		if err != nil {
			fmt.Printf("Failed to get cached OTA download URL: %v\n", err)
		} else {
			otaURL = otaURLs.DownloadURL
			otaMD5URL = otaURLs.MD5URL
		}
	}

	// 返回心跳响应，包含所有OTA相关信息
	c.JSON(http.StatusOK, gin.H{
		"status":                 "ok",
		"auto_ota_upgrade":       deviceSettings.AutoOTAUpgrade,
		"idle_update_start_hour": deviceSettings.IdleUpdateStartHour,
		"idle_update_end_hour":   deviceSettings.IdleUpdateEndHour,
		"ota_version":            otaVersion,
		"ota_required":           true,
		"ota_url":                otaURL,
		"ota_md5_url":            otaMD5URL,
	})
}

// getOTAVersionFromMinio 从MinIO获取OTA版本信息
func (h *Handler) getOTAVersionFromMinio() (string, error) {
	// 初始化MinIO客户端 - 使用OTA专用MinIO配置
	minioClient, err := minio.New(h.service.cfg.MinioOTA.Endpoint, &minio.Options{
		Creds:  credentials.NewStaticV4(h.service.cfg.MinioOTA.AccessKey, h.service.cfg.MinioOTA.SecretKey, ""),
		Secure: h.service.cfg.MinioOTA.UseSSL,
	})
	if err != nil {
		return "", fmt.Errorf("failed to initialize minio client: %v", err)
	}

	// 获取last_release_version文件的内容
	ctx := context.Background()
	obj, err := minioClient.GetObject(ctx, "otaworkspace", "last_release_version", minio.GetObjectOptions{})
	if err != nil {
		return "", fmt.Errorf("failed to get last_release_version object: %v", err)
	}
	defer obj.Close()

	// 读取文件内容
	buf := make([]byte, 1024)
	n, err := obj.Read(buf)
	if err != nil && err.Error() != "EOF" {
		return "", fmt.Errorf("failed to read last_release_version content: %v", err)
	}

	version := strings.TrimSpace(string(buf[:n]))
	return version, nil
}

// generateOTADownloadURL 生成OTA固件的下载链接
func (h *Handler) generateOTADownloadURL(version string) (string, error) {
	// 初始化MinIO客户端 - 使用OTA专用MinIO配置
	minioClient, err := minio.New(h.service.cfg.MinioOTA.Endpoint, &minio.Options{
		Creds:  credentials.NewStaticV4(h.service.cfg.MinioOTA.AccessKey, h.service.cfg.MinioOTA.SecretKey, ""),
		Secure: h.service.cfg.MinioOTA.UseSSL,
	})
	if err != nil {
		return "", fmt.Errorf("failed to initialize minio client: %v", err)
	}

	// 构造对象名称：core-image-minimal-aby-box-arm.rootfs-{version}.zip
	objectName := fmt.Sprintf("core-image-minimal-aby-box-arm.rootfs-%s.zip", version)

	// 验证文件是否存在
	ctx := context.Background()
	_, err = minioClient.StatObject(ctx, "otaworkspace", objectName, minio.StatObjectOptions{})
	if err != nil {
		return "", fmt.Errorf("OTA固件文件 %s 不存在: %v", objectName, err)
	}

	// 生成预签名URL，有效期7天
	presignedURL, err := minioClient.PresignedGetObject(ctx, "otaworkspace", objectName, time.Hour*24*7, nil)
	if err != nil {
		return "", fmt.Errorf("failed to generate presigned URL for %s: %v", objectName, err)
	}
	return presignedURL.String(), nil
}

// generateOTAMD5DownloadURL 生成OTA固件MD5文件的下载链接
func (h *Handler) generateOTAMD5DownloadURL(version string) (string, error) {
	// 初始化MinIO客户端 - 使用OTA专用MinIO配置
	minioClient, err := minio.New(h.service.cfg.MinioOTA.Endpoint, &minio.Options{
		Creds:  credentials.NewStaticV4(h.service.cfg.MinioOTA.AccessKey, h.service.cfg.MinioOTA.SecretKey, ""),
		Secure: h.service.cfg.MinioOTA.UseSSL,
	})
	if err != nil {
		return "", fmt.Errorf("failed to initialize minio client: %v", err)
	}

	// 构造MD5文件对象名称：core-image-minimal-aby-box-arm.rootfs-{version}.zip.md5
	md5ObjectName := fmt.Sprintf("core-image-minimal-aby-box-arm.rootfs-%s.zip.md5", version)

	// 验证MD5文件是否存在
	ctx := context.Background()
	_, err = minioClient.StatObject(ctx, "otaworkspace", md5ObjectName, minio.StatObjectOptions{})
	if err != nil {
		return "", fmt.Errorf("OTA固件MD5文件 %s 不存在: %v", md5ObjectName, err)
	}

	// 生成预签名URL，有效期7天
	presignedURL, err := minioClient.PresignedGetObject(ctx, "otaworkspace", md5ObjectName, time.Hour*24*7, nil)
	if err != nil {
		return "", fmt.Errorf("failed to generate presigned URL for %s: %v", md5ObjectName, err)
	}
	return presignedURL.String(), nil
}

// OTADownloadURLs OTA下载链接响应结构
type OTADownloadURLs struct {
	DownloadURL string `json:"download_url"`
	MD5URL      string `json:"md5_url"`
}

// getCachedOTADownloadURL 获取缓存的OTA下载链接，如果缓存无效则生成新的
func (h *Handler) getCachedOTADownloadURL(version string) (*OTADownloadURLs, error) {
	// 先尝试从缓存获取
	cache, err := h.service.GetOTADownloadCacheFromDB(version)
	if err == nil && cache != nil {
		// 检查缓存是否仍然有效（未过期）
		if time.Now().Before(cache.ExpiresAt) {
			return &OTADownloadURLs{
				DownloadURL: cache.DownloadURL,
				MD5URL:      cache.MD5URL,
			}, nil
		} else {
			fmt.Printf("缓存的OTA下载链接已过期: %s\n", version)
		}
	}

	// 缓存无效或不存在，生成新的下载链接
	downloadURL, err := h.generateOTADownloadURL(version)
	if err != nil {
		return nil, fmt.Errorf("生成OTA下载链接失败: %v", err)
	}

	// 生成MD5下载链接
	md5URL, err := h.generateOTAMD5DownloadURL(version)
	if err != nil {
		return nil, fmt.Errorf("生成OTA MD5下载链接失败: %v", err)
	}

	// 保存到缓存，有效期7天
	expiresAt := time.Now().Add(time.Hour * 24 * 7)
	if err := h.service.SaveOTADownloadCache(version, downloadURL, md5URL, expiresAt); err != nil {
		fmt.Printf("保存OTA下载链接缓存失败: %v\n", err)
		// 即使保存缓存失败，仍然返回下载链接
	} else {
		fmt.Printf("已保存OTA下载链接到缓存: %s\n", version)
	}

	return &OTADownloadURLs{
		DownloadURL: downloadURL,
		MD5URL:      md5URL,
	}, nil
}

func (h *Handler) DeviceTimezone(c *gin.Context) {
	deviceID := c.PostForm("device_id")
	if deviceID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "device_id is required"})
		return
	}

	timezone := c.PostForm("timezone")
	if timezone == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "timezone is required"})
		return
	}

	userID := c.PostForm("user_id")

	// 检查用户权限
	if userID != "" {
		err := h.service.CheckUserDeviceAccess(userID, deviceID)
		if err != nil {
			c.JSON(http.StatusForbidden, gin.H{"error": "没有权限更新该设备时区"})
			return
		}
	}

	if err := h.service.UpdateDeviceTimezone(deviceID, timezone); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"status": "ok"})
}

// GetDevice godoc
// @Summary 获取设备信息
// @Description 根据设备ID获取设备详细信息
// @Tags 设备管理
// @Accept json
// @Produce json
// @Param device_id path string true "设备ID"
// @Param user_id query string true "用户ID"
// @Success 200 {object} Device "获取成功"
// @Failure 403 {object} ErrorResponse "没有权限访问该设备"
// @Failure 404 {object} ErrorResponse "设备不存在"
// @Router /api/devices/{device_id} [get]
func (h *Handler) GetDevice(c *gin.Context) {
	deviceID := c.Param("device_id")
	userID := c.Query("user_id")

	// 检查用户权限
	if userID != "" {
		err := h.service.CheckUserDeviceAccess(userID, deviceID)
		if err != nil {
			c.JSON(http.StatusForbidden, gin.H{"error": "没有权限访问该设备"})
			return
		}
	}

	device, err := h.service.GetDevice(deviceID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "设备不存在"})
		return
	}

	c.JSON(http.StatusOK, device)
}

// ListUserDevices 获取用户设备列表
func (h *Handler) ListUserDevices(c *gin.Context) {
	userID := c.Param("user_id") // 直接使用字符串，不需要转换
	devices, err := h.service.ListUserDevices(userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, devices)
}

func (h *Handler) ListVideos(c *gin.Context) {
	// 实现视频列表查询
	// TODO: 添加分页和筛选功能
}

func (h *Handler) ListDeviceRecords(c *gin.Context) {
	deviceID := c.Param("device_id")
	var startTime, endTime *time.Time

	if startStr := c.Query("start_time"); startStr != "" {
		t, err := time.Parse(time.RFC3339, startStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid start_time format"})
			return
		}
		startTime = &t
	}

	if endStr := c.Query("end_time"); endStr != "" {
		t, err := time.Parse(time.RFC3339, endStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid end_time format"})
			return
		}
		endTime = &t
	}

	records, err := h.service.ListDeviceRecords(deviceID, startTime, endTime)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 为API响应处理记录 - 添加RFC3339格式的时间字符串以供客户端使用
	type RecordResponse struct {
		RecordShit
		StartTimeISO string  `json:"start_time_iso"`
		EndTimeISO   *string `json:"end_time_iso,omitempty"`
	}

	var responseRecords []RecordResponse
	for _, record := range records {
		startTimeISO := time.Unix(record.StartTime, 0).Format(time.RFC3339)
		var endTimeISO *string
		if record.EndTime != nil {
			tmp := time.Unix(*record.EndTime, 0).Format(time.RFC3339)
			endTimeISO = &tmp
		}
		responseRecords = append(responseRecords, RecordResponse{
			RecordShit:   record,
			StartTimeISO: startTimeISO,
			EndTimeISO:   endTimeISO,
		})
	}

	c.JSON(http.StatusOK, responseRecords)
}

func (h *Handler) ListCatRecords(c *gin.Context) {
	catID := c.Param("cat_id")
	records, err := h.service.ListCatRecords(catID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, records)
}

// GetUserByUsername godoc
// @Summary 通过用户名查询用户
// @Description 根据用户名获取用户信息
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param username query string true "用户名"
// @Success 200 {object} User "获取成功"
// @Failure 404 {object} ErrorResponse "用户不存在"
// @Router /api/users [get]
func (h *Handler) GetUserByUsername(c *gin.Context) {
	username := c.Query("username")
	if username == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "username is required"})
		return
	}

	user, err := h.service.GetUserByUsername(username)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "user not found"})
		return
	}

	// 清除敏感信息
	user.PasswordHash = ""
	c.JSON(http.StatusOK, user)
}

// GetUserByID 获取用户信息
func (h *Handler) GetUserByID(c *gin.Context) {
	userID := c.Param("user_id") // 直接使用字符串，不需要转换
	user, err := h.service.GetUser(userID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "user not found"})
		return
	}
	user.PasswordHash = ""
	c.JSON(http.StatusOK, user)
}

// CheckUsername godoc
// @Summary 检查用户名是否可用
// @Description 检查用户名是否已被注册
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param username query string true "用户名"
// @Success 200 {object} map[string]bool "检查结果"
// @Router /api/users/check [get]
func (h *Handler) CheckUsername(c *gin.Context) {
	username := c.Query("username")
	if username == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "username is required"})
		return
	}

	_, err := h.service.GetUserByUsername(username)
	if err == nil {
		c.JSON(http.StatusOK, gin.H{"available": false})
		return
	}
	c.JSON(http.StatusOK, gin.H{"available": true})
}

// ==================== 用户相关处理器 ====================

// UpdateUserProfile godoc
// @Summary 更新用户资料
// @Description 更新用户的详细资料
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param user_id path string true "用户ID"
// @Param user body User true "用户资料"
// @Success 200 {object} User "更新成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 404 {object} ErrorResponse "用户不存在"
// @Router /api/users/{user_id}/profile [put]
func (h *Handler) UpdateUserProfile(c *gin.Context) {
	userID := c.Param("user_id")

	// 先获取现有用户信息
	existingUser, err := h.service.GetUser(userID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	// 解析请求体
	var updateData struct {
		Nickname string `json:"nickname"`
		Email    string `json:"email"`
		Phone    string `json:"phone"`
	}

	if err := c.ShouldBindJSON(&updateData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 更新用户信息
	if updateData.Nickname != "" {
		existingUser.Nickname = updateData.Nickname
	}
	if updateData.Email != "" {
		existingUser.Email = updateData.Email
	}
	if updateData.Phone != "" {
		existingUser.Phone = updateData.Phone
	}

	// 保存更新
	if err := h.service.UpdateUser(existingUser); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 清除敏感信息
	existingUser.PasswordHash = ""
	c.JSON(http.StatusOK, existingUser)
}

// GetUserProfile 获取用户资料
func (h *Handler) GetUserProfile(c *gin.Context) {
	// 从路径参数获取 user_id
	userID := c.Param("user_id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "user_id is required",
			"data":    nil,
		})
		return
	}

	// 获取用户信息
	user, err := h.service.GetUser(userID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    http.StatusNotFound,
			"message": "user not found",
			"data":    nil,
		})
		return
	}

	// 清除敏感信息
	user.PasswordHash = ""

	// 返回标准格式的响应
	c.JSON(http.StatusOK, gin.H{
		"code":    http.StatusOK,
		"message": "success",
		"data": gin.H{
			"user_id":    user.UserID, // 确保返回 user_id
			"logto_id":   user.LogtoID,
			"username":   user.Username,
			"email":      user.Email,
			"phone":      user.Phone,
			"nickname":   user.Nickname,
			"status":     user.Status,
			"created_at": user.CreatedAt,
			"updated_at": user.UpdatedAt,
		},
	})
}

// GetUserSettings 获取用户设置
func (h *Handler) GetUserSettings(c *gin.Context) {
	userID := c.Param("user_id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "user_id is required",
			"data":    nil,
		})
		return
	}

	settings, err := h.service.GetUserSettings(userID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    http.StatusNotFound,
			"message": "settings not found",
			"data":    nil,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    http.StatusOK,
		"message": "success",
		"data":    settings,
	})
}

// UpdateUserSettings 更新用户设置
func (h *Handler) UpdateUserSettings(c *gin.Context) {
	userID := c.Param("user_id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "user_id is required",
			"data":    nil,
		})
		return
	}

	var settings UserSettings
	if err := c.ShouldBindJSON(&settings); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": err.Error(),
			"data":    nil,
		})
		return
	}

	// 设置 user_id 从路径参数，不需要在 JSON 中提供
	settings.UserID = userID

	if err := h.service.UpdateUserSettings(&settings); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"message": err.Error(),
			"data":    nil,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    http.StatusOK,
		"message": "success",
		"data":    settings,
	})
}

// ==================== 设备相关处理器 ====================

// GetDeviceConfig 获取设备配置
func (h *Handler) GetDeviceConfig(c *gin.Context) {
	deviceID := c.Param("device_id")
	userID := c.Query("user_id")

	// 检查用户权限
	if userID != "" {
		err := h.service.CheckUserDeviceAccess(userID, deviceID)
		if err != nil {
			c.JSON(http.StatusForbidden, gin.H{"error": "没有权限访问该设备配置"})
			return
		}
	}

	config, err := h.service.GetDeviceConfig(deviceID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "config not found"})
		return
	}

	c.JSON(http.StatusOK, config)
}

// ==================== 设备统计相关处理器 ====================

// GetDeviceDailyMetrics 获取设备每日统计
// func (h *Handler) GetDeviceDailyMetrics(c *gin.Context) {
// 	deviceID := c.Param("device_id")

// 	dateStr := c.Query("date")
// 	date, err := time.Parse("2006-01-02", dateStr)
// 	if err != nil {
// 		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid date format"})
// 		return
// 	}

// 	// 获取设备当日的统计数据
// 	metrics, err := h.service.GetDeviceDailyMetrics(deviceID, date)
// 	if err != nil {
// 		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
// 		return
// 	}

// 	c.JSON(http.StatusOK, metrics)
// }

// // GetDeviceMonthlyMetrics 获取设备月度统计
// func (h *Handler) GetDeviceMonthlyMetrics(c *gin.Context) {
// 	deviceID := c.Param("device_id")

// 	year, err := strconv.Atoi(c.Query("year"))
// 	if err != nil {
// 		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid year"})
// 		return
// 	}

// 	month, err := strconv.Atoi(c.Query("month"))
// 	if err != nil {
// 		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid month"})
// 		return
// 	}

// 	// 获取设备月度统计数据
// 	metrics, err := h.service.GetDeviceMonthlyMetrics(deviceID, year, month)
// 	if err != nil {
// 		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
// 		return
// 	}

// 	c.JSON(http.StatusOK, metrics)
// }

// ==================== 猫咪相关处理器 ====================

// UpdateCat 更新猫咪信息
func (h *Handler) UpdateCat(c *gin.Context) {
	catID := c.Param("cat_id")
	if catID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "cat_id is required"})
		return
	}

	// 获取用户ID（通过认证中间件设置）
	userID := c.GetString("user_id")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
		return
	}

	var req UpdateCatRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("Invalid request format: %v", err)})
		return
	}

	// 获取现有猫咪信息
	existingCat, err := h.service.GetCat(catID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Cat not found"})
		return
	}

	// 验证用户权限
	if existingCat.UserID != userID {
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
		return
	}

	// 更新猫咪信息
	updatedCat := *existingCat // 复制现有信息
	updatedCat.UpdatedAt = time.Now()

	// 更新提供的字段
	if req.Name != nil {
		updatedCat.Name = *req.Name
	}
	if req.Gender != nil {
		// 验证gender编码
		validGenders := []int8{0, 1, -1, 10, 11, -10, -11}
		isValidGender := false
		for _, validGender := range validGenders {
			if *req.Gender == validGender {
				isValidGender = true
				break
			}
		}
		if !isValidGender {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "Invalid gender code. Valid codes: 0(unknown), 1(male/unknown), -1(female/unknown), 10(male/neutered), 11(male/not neutered), -10(female/neutered), -11(female/not neutered)",
			})
			return
		}
		updatedCat.Gender = *req.Gender
	}
	if req.Birthday != nil && *req.Birthday != "" {
		parsedBirthday, err := time.Parse("2006-01-02", *req.Birthday)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid birthday format. Use YYYY-MM-DD"})
			return
		}
		updatedCat.Birthday = parsedBirthday
	}
	if req.Weight != nil && *req.Weight > 0 {
		updatedCat.Weight = *req.Weight
	}
	if req.Breed != nil {
		updatedCat.Breed = *req.Breed
	}
	if req.Color != nil {
		updatedCat.Color = *req.Color
	}
	if req.Status != nil {
		updatedCat.Status = *req.Status
	}

	// 处理头像上传
	if req.AvatarBase64 != nil && *req.AvatarBase64 != "" {
		// 上传头像到MinIO
		avatarPath, err := h.service.UploadCatAvatar(catID, *req.AvatarBase64)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("Failed to upload avatar: %v", err)})
			return
		}
		updatedCat.AvatarURL = avatarPath
	}

	if err := h.service.UpdateCat(&updatedCat); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to update cat: %v", err)})
		return
	}

	// 处理头像URL
	h.service.ProcessCatAvatarURL(&updatedCat)

	c.JSON(http.StatusOK, UpdateCatResponse{Cat: &updatedCat})
}

// DeleteCat godoc
// @Summary 删除猫咪
// @Description 软删除猫咪（将状态设置为已删除）
// @Tags 猫咪管理
// @Accept json
// @Produce json
// @Param cat_id path string true "猫咪ID"
// @Success 200 {object} map[string]interface{} "删除成功"
// @Failure 401 {object} ErrorResponse "未授权"
// @Failure 404 {object} ErrorResponse "猫咪不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/cats/{cat_id} [delete]
func (h *Handler) DeleteCat(c *gin.Context) {
	catID := c.Param("cat_id")

	// 获取用户ID（通过认证中间件设置）
	userID := c.GetString("user_id")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
		return
	}

	// 软删除猫咪
	if err := h.service.DeleteCat(catID, userID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to delete cat: %v", err)})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "Cat deleted successfully",
		"cat_id":  catID,
	})
}

// HideCat godoc
// @Summary 隐藏猫咪
// @Description 隐藏猫咪（将状态设置为隐藏，不在常规列表中显示）
// @Tags 猫咪管理
// @Accept json
// @Produce json
// @Param cat_id path string true "猫咪ID"
// @Success 200 {object} map[string]interface{} "隐藏成功"
// @Failure 401 {object} ErrorResponse "未授权"
// @Failure 404 {object} ErrorResponse "猫咪不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/cats/{cat_id}/hide [put]
func (h *Handler) HideCat(c *gin.Context) {
	catID := c.Param("cat_id")

	// 获取用户ID（通过认证中间件设置）
	userID := c.GetString("user_id")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
		return
	}

	// 隐藏猫咪
	if err := h.service.HideCat(catID, userID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to hide cat: %v", err)})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "Cat hidden successfully",
		"cat_id":  catID,
	})
}

// RestoreCat godoc
// @Summary 恢复猫咪
// @Description 恢复猫咪（取消隐藏，将状态设置为正常）
// @Tags 猫咪管理
// @Accept json
// @Produce json
// @Param cat_id path string true "猫咪ID"
// @Success 200 {object} map[string]interface{} "恢复成功"
// @Failure 401 {object} ErrorResponse "未授权"
// @Failure 404 {object} ErrorResponse "猫咪不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/cats/{cat_id}/restore [put]
func (h *Handler) RestoreCat(c *gin.Context) {
	catID := c.Param("cat_id")

	// 获取用户ID（通过认证中间件设置）
	userID := c.GetString("user_id")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
		return
	}

	// 恢复猫咪
	if err := h.service.RestoreCat(catID, userID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to restore cat: %v", err)})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "Cat restored successfully",
		"cat_id":  catID,
	})
}

// ListUserHiddenCats godoc
// @Summary 获取用户隐藏的猫咪列表
// @Description 获取当前认证用户的所有隐藏猫咪
// @Tags 猫咪管理
// @Accept json
// @Produce json
// @Success 200 {array} cat.Cat "获取成功"
// @Failure 401 {object} ErrorResponse "未授权"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/cats/hidden [get]
func (h *Handler) ListUserHiddenCats(c *gin.Context) {
	// 获取用户ID（通过认证中间件设置）
	userID := c.GetString("user_id")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
		return
	}

	cats, err := h.service.ListUserHiddenCats(userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to get hidden cats: %v", err)})
		return
	}

	// 为每个猫咪处理头像URL
	for i := range cats {
		h.service.ProcessCatAvatarURL(&cats[i])
	}

	c.JSON(http.StatusOK, cats)
}

// ListUserAllCats godoc
// @Summary 获取用户所有猫咪列表
// @Description 获取当前认证用户的所有猫咪（包括隐藏的，不包括已删除的）
// @Tags 猫咪管理
// @Accept json
// @Produce json
// @Success 200 {array} cat.Cat "获取成功"
// @Failure 401 {object} ErrorResponse "未授权"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/cats/all [get]
func (h *Handler) ListUserAllCats(c *gin.Context) {
	// 获取用户ID（通过认证中间件设置）
	userID := c.GetString("user_id")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
		return
	}

	cats, err := h.service.ListUserAllCats(userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to get all cats: %v", err)})
		return
	}

	// 为每个猫咪处理头像URL
	for i := range cats {
		h.service.ProcessCatAvatarURL(&cats[i])
	}

	c.JSON(http.StatusOK, cats)
}

// ==================== 统计相关处理器 ====================

// GetCatDailyMetrics 获取每日统计
func (h *Handler) GetCatDailyMetrics(c *gin.Context) {
	catID := c.Param("cat_id") // 已经是字符串类型
	dateStr := c.Query("date")
	date, err := time.Parse("2006-01-02", dateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid date format"})
		return
	}

	metrics, err := h.service.GetCatDailyMetrics(catID, date)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "metrics not found"})
		return
	}

	c.JSON(http.StatusOK, metrics)
}

// GetCatMonthlyMetrics 获取月度统计
func (h *Handler) GetCatMonthlyMetrics(c *gin.Context) {
	catID := c.Param("cat_id") // 已经是字符串类型
	year, err := strconv.Atoi(c.Query("year"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid year"})
		return
	}

	month, err := strconv.Atoi(c.Query("month"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid month"})
		return
	}

	metrics, err := h.service.GetCatMonthlyMetrics(catID, year, month)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "metrics not found"})
		return
	}

	c.JSON(http.StatusOK, metrics)
}

// ListCatAlerts 获取健康警报列表
func (h *Handler) ListCatAlerts(c *gin.Context) {
	catID := c.Param("cat_id") // 已经是字符串类型
	status, _ := strconv.ParseInt(c.DefaultQuery("status", "0"), 10, 8)
	alerts, err := h.service.ListCatAlerts(catID, int8(status))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, alerts)
}

// ListUserNotifications 获取用户通知
func (h *Handler) ListUserNotifications(c *gin.Context) {
	userID := c.Param("user_id") // 已经是字符串类型
	var isRead *bool
	if readStr := c.Query("is_read"); readStr != "" {
		read := readStr == "true"
		isRead = &read
	}

	notifications, err := h.service.ListUserNotifications(userID, isRead)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, notifications)
}

// DeviceStatus godoc
// @Summary 获取设备状态
// @Description 获取设备的当前状态（仅在线状态和基本信息）
// @Tags 设备管理
// @Accept json
// @Produce json
// @Param device_id path string true "设备ID"
// @Param user_id query string true "用户ID"
// @Success 200 {object} map[string]interface{} "设备状态信息"
// @Failure 403 {object} ErrorResponse "没有权限访问该设备"
// @Failure 404 {object} ErrorResponse "设备不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/devices/{device_id}/status [get]
func (h *Handler) GetDeviceStatus(c *gin.Context) {
	deviceID := c.Param("device_id")
	userID := c.Query("user_id")

	// 检查用户权限
	if userID != "" {
		err := h.service.CheckUserDeviceAccess(userID, deviceID)
		if err != nil {
			c.JSON(http.StatusForbidden, gin.H{"error": "没有权限访问该设备"})
			return
		}
	}

	// 获取设备信息
	device, err := h.service.GetDevice(deviceID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Device not found"})
		return
	}

	// 获取设备状态
	status, err := h.service.GetDeviceStatus(deviceID)
	if err != nil && err != gorm.ErrRecordNotFound {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 如果没有状态记录，创建一个默认的
	if status == nil {
		status = &DeviceStatus{
			DeviceID: deviceID,
			Online:   false,
		}
	}

	// 检查设备是否在线（基于心跳时间）
	isOnline, _ := h.service.IsDeviceOnline(deviceID)
	status.Online = isOnline

	// 组装返回数据（仅基本状态信息）
	result := map[string]interface{}{
		"device_id":      deviceID,
		"name":           device.Name,
		"model":          device.Model,
		"firmware":       device.FirmwareVersion,
		"timezone":       device.Timezone,
		"online":         status.Online,
		"last_heartbeat": device.LastHeartbeat,
		"ipv4":           status.IPv4,
		"ipv6":           status.IPv6,
		"updated_at":     status.UpdatedAt,
	}

	c.JSON(http.StatusOK, result)
}

// GetDeviceStatistics godoc
// @Summary 获取设备统计数据
// @Description 获取设备的统计信息（在线率、信号强度平均值等）
// @Tags 设备管理
// @Accept json
// @Produce json
// @Param device_id path string true "设备ID"
// @Param user_id query string true "用户ID"
// @Success 200 {object} map[string]interface{} "设备统计信息"
// @Failure 403 {object} ErrorResponse "没有权限访问该设备"
// @Failure 404 {object} ErrorResponse "设备不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/devices/{device_id}/statistics [get]
func (h *Handler) GetDeviceStatistics(c *gin.Context) {
	deviceID := c.Param("device_id")
	userID := c.Query("user_id")

	// 检查用户权限
	if userID != "" {
		err := h.service.CheckUserDeviceAccess(userID, deviceID)
		if err != nil {
			c.JSON(http.StatusForbidden, gin.H{"error": "没有权限访问该设备"})
			return
		}
	}

	// 检查设备是否存在
	_, err := h.service.GetDevice(deviceID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Device not found"})
		return
	}

	// 获取设备统计数据
	// stats, err := h.service.GetDeviceStatusStatistics(deviceID)
	// if err != nil && err != gorm.ErrRecordNotFound {
	// 	c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
	// 	return
	// }
	var stats *DeviceStatusStatistics // Declare stats; it will be nil initially

	// 如果没有统计数据，创建一个默认的
	if stats == nil { // This condition will now always be true
		stats = &DeviceStatusStatistics{
			DeviceID: deviceID,
			// OnlineRate, PowerSupplyRate, SignalStrengthAvg, StorageUsage, UpdatedAt will be zero-valued
		}
	}

	// 组装返回数据
	result := map[string]interface{}{
		"device_id":           deviceID,
		"online_rate":         stats.OnlineRate,
		"power_supply_rate":   stats.PowerSupplyRate,
		"signal_strength_avg": stats.SignalStrengthAvg,
		"storage_usage":       stats.StorageUsage,
		"updated_at":          stats.UpdatedAt,
	}

	c.JSON(http.StatusOK, result)
}

// UpdateDeviceConfig 更新设备配置
func (h *Handler) UpdateDeviceConfig(c *gin.Context) {
	var config DeviceConfig
	if err := c.ShouldBindJSON(&config); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	deviceID := c.Param("device_id")
	userID := c.Query("user_id")

	// 检查请求中的设备ID与URL中的是否匹配
	if deviceID != config.DeviceID {
		c.JSON(http.StatusBadRequest, gin.H{"error": "设备ID不匹配"})
		return
	}

	// 检查用户权限
	if userID != "" {
		err := h.service.CheckUserDeviceAccess(userID, deviceID)
		if err != nil {
			c.JSON(http.StatusForbidden, gin.H{"error": "没有权限更新该设备配置"})
			return
		}
	}

	if err := h.service.UpdateDeviceConfig(&config); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, config)
}

// CreateUserHardware godoc
// @Summary Add a new user hardware association
// @Description Add a new association between a user and hardware device
// @Tags devices
// @Accept json
// @Produce json
// @Param body body UserHardwareRequest true "User Hardware information"
// @Success 200 {object} UserHardware
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/devices [post]
func (h *Handler) CreateUserHardware(c *gin.Context) {
	var request UserHardwareRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid request data",
			Error:   err.Error(),
		})
		return
	}

	// Validate required fields
	if request.UserID == "" || request.HardwareSN == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "User ID and Hardware SN are required",
		})
		return
	}

	userHardware, err := h.service.CreateUserHardwareFromRequest(&request)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    http.StatusInternalServerError,
			Message: "Failed to create user hardware association",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, userHardware)
}

// ListUserHardware 获取用户硬件列表
func (h *Handler) ListUserHardware(c *gin.Context) {
	userID := c.Param("user_id") // 直接使用字符串，不需要转换
	hardware, err := h.service.ListUserHardware(userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, hardware)
}

// GetHardwareUser godoc
// @Summary 获取硬件关联的用户
// @Description 根据硬件序列号获取关联的用户信息
// @Tags 用户硬件关联
// @Accept json
// @Produce json
// @Param hardware_sn path string true "硬件序列号"
// @Success 200 {object} HardwareUserResponse "获取成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 404 {object} ErrorResponse "未找到记录"
// @Router /api/hardware/{hardware_sn}/user [get]
func (h *Handler) GetHardwareUser(c *gin.Context) {
	hardwareSN := c.Param("hardware_sn")
	if hardwareSN == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "hardware_sn is required"})
		return
	}

	hardware, err := h.service.GetHardwareUser(hardwareSN)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "hardware not found"})
		return
	}

	c.JSON(http.StatusOK, hardware)
}

// CheckUserHardwareExists godoc
// @Summary 检查用户硬件关联是否存在
// @Description 检查指定用户和硬件序列号的关联是否存在
// @Tags 用户硬件关联
// @Accept json
// @Produce json
// @Param user_id query string true "用户ID"
// @Param hardware_sn query string true "硬件序列号"
// @Success 200 {object} map[string]bool "检查结果"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Router /api/devices/check [get]
func (h *Handler) CheckUserHardwareExists(c *gin.Context) {
	userID := c.Query("user_id")
	hardwareSN := c.Query("hardware_sn")

	if userID == "" || hardwareSN == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "用户ID和硬件序列号为必填项",
		})
		return
	}

	exists, err := h.service.CheckUserHardwareExists(userID, hardwareSN)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    http.StatusInternalServerError,
			Message: "检查用户硬件关联时出错",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"exists": exists,
	})
}

// ListUsers godoc
// @Summary 获取用户列表
// @Description 获取所有用户的列表
// @Tags 用户管理
// @Accept json
// @Produce json
// @Success 200 {object} BaseResponse{data=[]User} "获取成功"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/users [get]
func (h *Handler) ListUsers(c *gin.Context) {
	users, err := h.service.ListUsers()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"message": err.Error(),
			"data":    nil,
		})
		return
	}

	// 清除敏感信息
	for i := range users {
		users[i].PasswordHash = ""
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    http.StatusOK,
		"message": "success",
		"data":    users,
	})
}

// RegisterClient godoc
// @Summary 注册新客户端
// @Description 注册新的客户端设备
// @Tags 客户端管理
// @Accept json
// @Produce json
// @Param request body ClientRegisterRequest true "客户端注册信息"
// @Success 200 {object} BaseResponse{data=ClientResponse} "注册成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Router /api/clients/register [post]
func (h *Handler) RegisterClient(c *gin.Context) {
	var req ClientRegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid parameters",
			Error:   err.Error(),
		})
		return
	}

	client, err := h.service.RegisterClient(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    http.StatusInternalServerError,
			Message: "Failed to register client",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    http.StatusOK,
		"message": "Client registered successfully",
		"data":    client,
	})
}

// RegisterClientToken godoc
// @Summary 注册客户端推送令牌
// @Description 注册或更新客户端的推送令牌
// @Tags 客户端管理
// @Accept json
// @Produce json
// @Param client_id path string true "客户端ID"
// @Param request body ClientTokenRequest true "令牌信息"
// @Success 200 {object} BaseResponse "注册成功"
// @Router /api/clients/{client_id}/token [post]
func (h *Handler) RegisterClientToken(c *gin.Context) {
	clientID := c.Param("client_id")
	var req ClientTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid parameters",
			Error:   err.Error(),
		})
		return
	}

	if err := h.service.RegisterClientToken(clientID, &req); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    http.StatusInternalServerError,
			Message: "Failed to register client token",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, BaseResponse{
		Code:    http.StatusOK,
		Message: "Client token registered successfully",
	})
}

// UpdateClient godoc
// @Summary 更新客户端信息
// @Description 更新客户端设备信息
// @Tags 客户端管理
// @Accept json
// @Produce json
// @Param client_id path string true "客户端ID"
// @Param request body ClientUpdateRequest true "更新信息"
// @Success 200 {object} BaseResponse{data=ClientResponse} "更新成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 404 {object} ErrorResponse "客户端不存在"
// @Router /api/clients/{client_id} [put]
func (h *Handler) UpdateClient(c *gin.Context) {
	clientID := c.Param("client_id")
	var req ClientUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid parameters",
			Error:   err.Error(),
		})
		return
	}

	client, err := h.service.UpdateClient(clientID, &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    http.StatusInternalServerError,
			Message: "Failed to update client",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    http.StatusOK,
		"message": "Client updated successfully",
		"data":    client,
	})
}

// GetClient godoc
// @Summary 获取客户端信息
// @Description 获取指定客户端的详细信息
// @Tags 客户端管理
// @Accept json
// @Produce json
// @Param client_id path string true "客户端ID"
// @Success 200 {object} BaseResponse{data=ClientResponse} "获取成功"
// @Failure 404 {object} ErrorResponse "客户端不存在"
// @Router /api/clients/{client_id} [get]
func (h *Handler) GetClient(c *gin.Context) {
	clientID := c.Param("client_id")
	client, err := h.service.GetClient(clientID)
	if err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{
			Code:    http.StatusNotFound,
			Message: "Client not found",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    http.StatusOK,
		"message": "Success",
		"data":    client,
	})
}

// ListUserClients godoc
// @Summary 获取用户的所有客户端
// @Description 获取指定用户的所有客户端列表
// @Tags 客户端管理
// @Accept json
// @Produce json
// @Param user_id query string true "用户ID"
// @Success 200 {object} BaseResponse{data=[]ClientResponse} "获取成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Router /api/clients [get]
func (h *Handler) ListUserClients(c *gin.Context) {
	userID := c.Query("user_id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "user_id is required",
		})
		return
	}

	clients, err := h.service.ListUserClients(userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    http.StatusInternalServerError,
			Message: "Failed to get user clients",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    http.StatusOK,
		"message": "Success",
		"data":    clients,
	})
}

// DeleteClientToken godoc
// @Summary 删除客户端令牌
// @Description 删除指定客户端的推送令牌
// @Tags 客户端管理
// @Accept json
// @Produce json
// @Param client_id path string true "客户端ID"
// @Success 200 {object} BaseResponse "删除成功"
// @Failure 404 {object} ErrorResponse "客户端不存在"
// @Router /api/clients/{client_id}/token [delete]
func (h *Handler) DeleteClientToken(c *gin.Context) {
	clientID := c.Param("client_id")
	if err := h.service.DeleteClientToken(clientID); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    http.StatusInternalServerError,
			Message: "Failed to delete client token",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, BaseResponse{
		Code:    http.StatusOK,
		Message: "Client token deleted successfully",
	})
}

// GetClientToken godoc
// @Summary 获取客户端令牌信息
// @Description 获取指定客户端的推送令牌信息
// @Tags 客户端管理
// @Accept json
// @Produce json
// @Param client_id path string true "客户端ID"
// @Success 200 {object} BaseResponse{data=ClientTokenResponse} "获取成功"
// @Failure 404 {object} ErrorResponse "令牌不存在"
// @Router /api/clients/{client_id}/token [get]
func (h *Handler) GetClientToken(c *gin.Context) {
	clientID := c.Param("client_id")
	token, err := h.service.GetClientToken(clientID)
	if err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{
			Code:    http.StatusNotFound,
			Message: "Client token not found",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    http.StatusOK,
		"message": "Success",
		"data":    token,
	})
}

// ClientHeartbeat godoc
// @Summary 客户端心跳
// @Description 更新客户端的活跃状态
// @Tags 客户端管理
// @Accept json
// @Produce json
// @Param client_id path string true "客户端ID"
// @Param request body ClientHeartbeatRequest true "心跳信息"
// @Success 200 {object} BaseResponse "更新成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Router /api/clients/{client_id}/heartbeat [post]
func (h *Handler) ClientHeartbeat(c *gin.Context) {
	clientID := c.Param("client_id")
	var req ClientHeartbeatRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid parameters",
			Error:   err.Error(),
		})
		return
	}

	if err := h.service.HandleClientHeartbeat(clientID, &req); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    http.StatusInternalServerError,
			Message: "Failed to update client heartbeat",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, BaseResponse{
		Code:    http.StatusOK,
		Message: "Client heartbeat updated successfully",
	})
}

// UpdateClientStatus godoc
// @Summary 更新客户端状态
// @Description 更新客户端的状态信息
// @Tags 客户端管理
// @Accept json
// @Produce json
// @Param client_id path string true "客户端ID"
// @Param request body ClientStatusRequest true "状态信息"
// @Success 200 {object} BaseResponse "更新成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Router /api/clients/{client_id}/status [put]
func (h *Handler) UpdateClientStatus(c *gin.Context) {
	clientID := c.Param("client_id")
	var req ClientStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid parameters",
			Error:   err.Error(),
		})
		return
	}

	if err := h.service.UpdateClientStatus(clientID, &req); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    http.StatusInternalServerError,
			Message: "Failed to update client status",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, BaseResponse{
		Code:    http.StatusOK,
		Message: "Client status updated successfully",
	})
}

// DeleteClient godoc
// @Summary 删除客户端
// @Description 删除指定的客户端及其关联的令牌
// @Tags 客户端管理
// @Accept json
// @Produce json
// @Param client_id path string true "客户端ID"
// @Success 200 {object} BaseResponse "删除成功"
// @Failure 500 {object} ErrorResponse "服务器错误"
// @Router /api/clients/{client_id} [delete]
func (h *Handler) DeleteClient(c *gin.Context) {
	clientID := c.Param("client_id")
	if err := h.service.DeleteClient(clientID); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    http.StatusInternalServerError,
			Message: "Failed to delete client",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, BaseResponse{
		Code:    http.StatusOK,
		Message: "Client deleted successfully",
	})
}

// CreateCatRequest 创建猫咪请求
type CreateCatRequest struct {
	Name         string   `json:"name" binding:"required"`
	Gender       int8     `json:"gender"`
	Birthday     *string  `json:"birthday,omitempty"`      // RFC3339 date format, optional
	Weight       *float64 `json:"weight,omitempty"`        // Initial weight, optional
	PhotosBase64 []string `json:"photos_base64,omitempty"` // Base64 encoded cat photos for AI analysis
	AvatarBase64 *string  `json:"avatar_base64,omitempty"` // Base64 encoded avatar image, optional
	Breed        *string  `json:"breed,omitempty"`         // TBD: Will be obtained through AI analysis of photos
	Color        *string  `json:"color,omitempty"`         // TBD: Will be obtained through AI analysis of photos
}

// CreateCatResponse 创建猫咪响应
type CreateCatResponse struct {
	Status  string   `json:"status"`
	Message string   `json:"message"`
	Data    *cat.Cat `json:"data"`
}

// UpdateCatRequest 更新猫咪请求
type UpdateCatRequest struct {
	Name         *string  `json:"name,omitempty"`
	Gender       *int8    `json:"gender,omitempty"`
	Birthday     *string  `json:"birthday,omitempty"`      // YYYY-MM-DD format, optional
	Weight       *float64 `json:"weight,omitempty"`        // Weight, optional
	Breed        *string  `json:"breed,omitempty"`         // Breed, optional
	Color        *string  `json:"color,omitempty"`         // Color, optional
	Status       *int8    `json:"status,omitempty"`        // Status, optional
	AvatarBase64 *string  `json:"avatar_base64,omitempty"` // Base64 encoded avatar image, optional
}

// UpdateCatResponse 更新猫咪响应
type UpdateCatResponse struct {
	*cat.Cat
}

// CheckVideoStatic godoc
// @Summary 检查视频是否为静态
// @Description 检查指定视频是否为静态视频，并更新数据库中的分析结果
// @Tags 视频记录
// @Accept json
// @Produce json
// @Param video_id path string true "视频ID"
// @Param user_id body string true "用户ID"
// @Success 200 {object} gin.H "检查成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/records/videos/{video_id}/static [post]
func (h *Handler) CheckVideoStatic(c *gin.Context) {
	videoID := c.Param("video_id")
	if videoID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "video_id is required"})
		return
	}

	// 获取请求体中的 userID
	var req struct {
		UserID string `json:"user_id" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request format"})
		return
	}

	userID := req.UserID

	// 获取视频记录
	record, err := h.service.GetRecordShit(videoID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("failed to get video record: %v", err)})
		return
	}

	// 同步调用AI服务进行分析并更新数据库
	analysis, err := h.service.AnalyzeVideoAndUpdateDB(record, userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("failed to analyze video: %v", err)})
		return
	}

	// 返回分析结果
	c.JSON(http.StatusOK, gin.H{
		"is_static": analysis.BehaviorType == "static_video",
		"analysis":  analysis,
	})
}

// GetDeviceAutoOTASettings godoc
// @Summary 获取设备设置
// @Description 获取设备的各项设置，包括是否开启自动OTA更新、闲时更新时间等
// @Tags 设备管理
// @Accept json
// @Produce json
// @Param device_id path string true "设备ID"
// @Success 200 {object} DeviceAllSettings "获取成功"
// @Failure 404 {object} ErrorResponse "设备不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/devices/{device_id}/setting [get]
func (h *Handler) GetDeviceAutoOTASettings(c *gin.Context) {
	deviceID := c.Param("device_id")
	if deviceID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "device_id is required"})
		return
	}

	// 获取设备所有设置
	settings, err := h.service.GetDeviceAutoOTASettings(deviceID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, settings)
}

// UpdateDeviceAutoOTASettings godoc
// @Summary 更新设备设置
// @Description 更新设备的各项设置，包括是否开启自动OTA更新、闲时更新时间等
// @Tags 设备管理
// @Accept json
// @Produce json
// @Param device_id path string true "设备ID"
// @Param request body DeviceSettingsUpdateRequest true "更新设置"
// @Success 200 {object} DeviceAllSettings "更新成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 404 {object} ErrorResponse "设备不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/devices/{device_id}/setting [put]
func (h *Handler) UpdateDeviceAutoOTASettings(c *gin.Context) {
	deviceID := c.Param("device_id")
	if deviceID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "device_id is required"})
		return
	}

	var request DeviceSettingsUpdateRequest
	if err := c.BindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request format"})
		return
	}

	// 验证请求参数
	if request.AutoOTAUpgrade != nil && *request.AutoOTAUpgrade != "on" && *request.AutoOTAUpgrade != "off" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "auto_ota_upgrade must be 'on' or 'off'"})
		return
	}

	if request.IdleUpdateStartHour != nil && (*request.IdleUpdateStartHour < 0 || *request.IdleUpdateStartHour > 23) {
		c.JSON(http.StatusBadRequest, gin.H{"error": "idle_update_start_hour must be between 0 and 23"})
		return
	}

	if request.IdleUpdateEndHour != nil && (*request.IdleUpdateEndHour < 0 || *request.IdleUpdateEndHour > 23) {
		c.JSON(http.StatusBadRequest, gin.H{"error": "idle_update_end_hour must be between 0 and 23"})
		return
	}

	// 获取当前设置
	currentSettings, err := h.service.GetDeviceAutoOTASettings(deviceID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 更新设置
	if request.AutoOTAUpgrade != nil {
		currentSettings.AutoOTAUpgrade = *request.AutoOTAUpgrade
	}
	if request.IdleUpdateStartHour != nil {
		currentSettings.IdleUpdateStartHour = *request.IdleUpdateStartHour
	}
	if request.IdleUpdateEndHour != nil {
		currentSettings.IdleUpdateEndHour = *request.IdleUpdateEndHour
	}

	// 保存设置
	if err := h.service.UpdateDeviceAllSettings(deviceID, currentSettings); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, currentSettings)
}

// DeviceSettingsUpdateRequest 设备设置更新请求
type DeviceSettingsUpdateRequest struct {
	AutoOTAUpgrade      *string `json:"auto_ota_upgrade,omitempty"`       // "on" or "off"，可选
	IdleUpdateStartHour *int8   `json:"idle_update_start_hour,omitempty"` // 闲时更新开始时间（小时，0-23），可选
	IdleUpdateEndHour   *int8   `json:"idle_update_end_hour,omitempty"`   // 闲时更新结束时间（小时，0-23），可选
}

// ==================== 家庭组相关处理器 ====================

// CreateFamilyGroup godoc
// @Summary 创建家庭组
// @Description 创建新的家庭组，创建者将自动成为组拥有者
// @Tags 家庭组
// @Accept json
// @Produce json
// @Param request body FamilyGroupCreateRequest true "家庭组信息"
// @Param user_id query string true "用户ID"
// @Success 200 {object} BaseResponse{data=FamilyGroup} "创建成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/family-groups [post]
func (h *Handler) CreateFamilyGroup(c *gin.Context) {
	userID := c.Query("user_id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "user_id is required"})
		return
	}

	var req FamilyGroupCreateRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request format"})
		return
	}

	group, err := h.service.CreateFamilyGroup(userID, &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, &BaseResponse{
		Status:  "success",
		Message: "家庭组创建成功",
		Data:    group,
	})
}

// GetFamilyGroup godoc
// @Summary 获取家庭组详情
// @Description 获取指定家庭组的详细信息，包括成员和设备
// @Tags 家庭组
// @Accept json
// @Produce json
// @Param group_id path string true "家庭组ID"
// @Param user_id query string true "用户ID"
// @Success 200 {object} BaseResponse{data=FamilyGroupWithDetails} "获取成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 403 {object} ErrorResponse "权限不足"
// @Failure 404 {object} ErrorResponse "家庭组不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/family-groups/{group_id} [get]
func (h *Handler) GetFamilyGroup(c *gin.Context) {
	groupID := c.Param("group_id")
	userID := c.Query("user_id")

	if groupID == "" || userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "group_id and user_id are required"})
		return
	}

	group, err := h.service.GetFamilyGroup(userID, groupID)
	if err != nil {
		statusCode := http.StatusInternalServerError
		if strings.Contains(err.Error(), "不是该家庭组的成员") {
			statusCode = http.StatusForbidden
		} else if strings.Contains(err.Error(), "不存在") {
			statusCode = http.StatusNotFound
		}
		c.JSON(statusCode, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, &BaseResponse{
		Status:  "success",
		Message: "获取家庭组详情成功",
		Data:    group,
	})
}

// ListUserFamilyGroups godoc
// @Summary 获取用户的所有家庭组
// @Description 获取指定用户所属的所有家庭组列表
// @Tags 家庭组
// @Accept json
// @Produce json
// @Param user_id query string true "用户ID"
// @Success 200 {object} BaseResponse{data=[]FamilyGroupResponse} "获取成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/family-groups [get]
func (h *Handler) ListUserFamilyGroups(c *gin.Context) {
	userID := c.Query("user_id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "user_id is required"})
		return
	}

	groups, err := h.service.ListUserFamilyGroups(userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, &BaseResponse{
		Status:  "success",
		Message: "获取家庭组列表成功",
		Data:    groups,
	})
}

// UpdateFamilyGroup godoc
// @Summary 更新家庭组信息
// @Description 更新指定家庭组的基本信息，需要管理员或拥有者权限
// @Tags 家庭组
// @Accept json
// @Produce json
// @Param group_id path string true "家庭组ID"
// @Param user_id query string true "操作用户ID"
// @Param request body FamilyGroupUpdateRequest true "更新信息"
// @Success 200 {object} BaseResponse{data=FamilyGroup} "更新成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 403 {object} ErrorResponse "权限不足"
// @Failure 404 {object} ErrorResponse "家庭组不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/family-groups/{group_id} [put]
func (h *Handler) UpdateFamilyGroup(c *gin.Context) {
	groupID := c.Param("group_id")
	userID := c.Query("user_id")

	if groupID == "" || userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "group_id and user_id are required"})
		return
	}

	var req FamilyGroupUpdateRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request format"})
		return
	}

	group, err := h.service.UpdateFamilyGroup(userID, groupID, &req)
	if err != nil {
		statusCode := http.StatusInternalServerError
		if strings.Contains(err.Error(), "无权更新") || strings.Contains(err.Error(), "没有权限") {
			statusCode = http.StatusForbidden
		} else if strings.Contains(err.Error(), "不存在") {
			statusCode = http.StatusNotFound
		}
		c.JSON(statusCode, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, &BaseResponse{
		Status:  "success",
		Message: "家庭组信息更新成功",
		Data:    group,
	})
}

// DeleteFamilyGroup godoc
// @Summary 删除家庭组
// @Description 删除指定家庭组，需要拥有者权限
// @Tags 家庭组
// @Accept json
// @Produce json
// @Param group_id path string true "家庭组ID"
// @Param user_id query string true "操作用户ID"
// @Success 200 {object} BaseResponse "删除成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 403 {object} ErrorResponse "权限不足"
// @Failure 404 {object} ErrorResponse "家庭组不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/family-groups/{group_id} [delete]
func (h *Handler) DeleteFamilyGroup(c *gin.Context) {
	groupID := c.Param("group_id")
	userID := c.Query("user_id")

	if groupID == "" || userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "group_id and user_id are required"})
		return
	}

	if err := h.service.DeleteFamilyGroup(userID, groupID); err != nil {
		statusCode := http.StatusInternalServerError
		if strings.Contains(err.Error(), "无权删除") || strings.Contains(err.Error(), "只有家庭组拥有者") {
			statusCode = http.StatusForbidden
		} else if strings.Contains(err.Error(), "不存在") {
			statusCode = http.StatusNotFound
		}
		c.JSON(statusCode, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, &BaseResponse{
		Status:  "success",
		Message: "家庭组删除成功",
	})
}

// AddFamilyGroupMember godoc
// @Summary 添加家庭组成员
// @Description 向指定家庭组添加新成员，需要管理员或拥有者权限
// @Tags 家庭组
// @Accept json
// @Produce json
// @Param group_id path string true "家庭组ID"
// @Param user_id query string true "操作用户ID"
// @Param request body FamilyGroupMemberAddRequest true "成员信息"
// @Success 200 {object} BaseResponse{data=FamilyGroupMember} "添加成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 403 {object} ErrorResponse "权限不足"
// @Failure 404 {object} ErrorResponse "家庭组或用户不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/family-groups/{group_id}/members [post]
func (h *Handler) AddFamilyGroupMember(c *gin.Context) {
	groupID := c.Param("group_id")
	userID := c.Query("user_id")

	if groupID == "" || userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "group_id and user_id are required"})
		return
	}

	var req FamilyGroupMemberAddRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request format"})
		return
	}

	member, err := h.service.AddFamilyGroupMember(userID, groupID, &req)
	if err != nil {
		statusCode := http.StatusInternalServerError
		if strings.Contains(err.Error(), "无权添加") || strings.Contains(err.Error(), "没有权限") {
			statusCode = http.StatusForbidden
		} else if strings.Contains(err.Error(), "不存在") {
			statusCode = http.StatusNotFound
		} else if strings.Contains(err.Error(), "已经是家庭组成员") {
			statusCode = http.StatusBadRequest
		}
		c.JSON(statusCode, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, &BaseResponse{
		Status:  "success",
		Message: "添加家庭组成员成功",
		Data:    member,
	})
}

// UpdateFamilyGroupMember godoc
// @Summary 更新家庭组成员信息
// @Description 更新指定家庭组成员的信息，需要适当权限
// @Tags 家庭组
// @Accept json
// @Produce json
// @Param group_id path string true "家庭组ID"
// @Param member_id path string true "成员用户ID"
// @Param user_id query string true "操作用户ID"
// @Param request body FamilyGroupMemberUpdateRequest true "更新信息"
// @Success 200 {object} BaseResponse{data=FamilyGroupMember} "更新成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 403 {object} ErrorResponse "权限不足"
// @Failure 404 {object} ErrorResponse "家庭组或成员不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/family-groups/{group_id}/members/{member_id} [put]
func (h *Handler) UpdateFamilyGroupMember(c *gin.Context) {
	groupID := c.Param("group_id")
	memberID := c.Param("member_id")
	userID := c.Query("user_id")

	if groupID == "" || memberID == "" || userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "group_id, member_id, and user_id are required"})
		return
	}

	var req FamilyGroupMemberUpdateRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request format"})
		return
	}

	member, err := h.service.UpdateFamilyGroupMember(userID, groupID, memberID, &req)
	if err != nil {
		statusCode := http.StatusInternalServerError
		if strings.Contains(err.Error(), "没有权限") {
			statusCode = http.StatusForbidden
		} else if strings.Contains(err.Error(), "不存在") {
			statusCode = http.StatusNotFound
		}
		c.JSON(statusCode, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, &BaseResponse{
		Status:  "success",
		Message: "更新家庭组成员信息成功",
		Data:    member,
	})
}

// RemoveFamilyGroupMember godoc
// @Summary 移除家庭组成员
// @Description 从指定家庭组中移除成员，需要适当权限
// @Tags 家庭组
// @Accept json
// @Produce json
// @Param group_id path string true "家庭组ID"
// @Param member_id path string true "成员用户ID"
// @Param user_id query string true "操作用户ID"
// @Success 200 {object} BaseResponse "移除成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 403 {object} ErrorResponse "权限不足"
// @Failure 404 {object} ErrorResponse "家庭组或成员不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/family-groups/{group_id}/members/{member_id} [delete]
func (h *Handler) RemoveFamilyGroupMember(c *gin.Context) {
	groupID := c.Param("group_id")
	memberID := c.Param("member_id")
	userID := c.Query("user_id")

	if groupID == "" || memberID == "" || userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "group_id, member_id, and user_id are required"})
		return
	}

	if err := h.service.RemoveFamilyGroupMember(userID, groupID, memberID); err != nil {
		statusCode := http.StatusInternalServerError
		if strings.Contains(err.Error(), "没有权限") || strings.Contains(err.Error(), "无权移除") {
			statusCode = http.StatusForbidden
		} else if strings.Contains(err.Error(), "不存在") {
			statusCode = http.StatusNotFound
		}
		c.JSON(statusCode, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, &BaseResponse{
		Status:  "success",
		Message: "移除家庭组成员成功",
	})
}

// AddFamilyGroupDevice godoc
// @Summary 添加设备到家庭组
// @Description 向指定家庭组添加设备，需要管理员或拥有者权限
// @Tags 家庭组
// @Accept json
// @Produce json
// @Param group_id path string true "家庭组ID"
// @Param user_id query string true "操作用户ID"
// @Param request body FamilyGroupDeviceAddRequest true "设备信息"
// @Success 200 {object} BaseResponse{data=FamilyGroupDevice} "添加成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 403 {object} ErrorResponse "权限不足"
// @Failure 404 {object} ErrorResponse "家庭组或设备不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/family-groups/{group_id}/devices [post]
func (h *Handler) AddFamilyGroupDevice(c *gin.Context) {
	groupID := c.Param("group_id")
	userID := c.Query("user_id")

	if groupID == "" || userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "group_id and user_id are required"})
		return
	}

	var req FamilyGroupDeviceAddRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request format"})
		return
	}

	device, err := h.service.AddFamilyGroupDevice(userID, groupID, &req)
	if err != nil {
		statusCode := http.StatusInternalServerError
		if strings.Contains(err.Error(), "无权添加") || strings.Contains(err.Error(), "没有权限") {
			statusCode = http.StatusForbidden
		} else if strings.Contains(err.Error(), "不存在") {
			statusCode = http.StatusNotFound
		} else if strings.Contains(err.Error(), "已经在家庭组中") {
			statusCode = http.StatusBadRequest
		}
		c.JSON(statusCode, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, &BaseResponse{
		Status:  "success",
		Message: "添加设备到家庭组成功",
		Data:    device,
	})
}

// RemoveFamilyGroupDevice godoc
// @Summary 从家庭组移除设备
// @Description 从指定家庭组中移除设备，需要管理员或拥有者权限
// @Tags 家庭组
// @Accept json
// @Produce json
// @Param group_id path string true "家庭组ID"
// @Param device_id path string true "设备ID"
// @Param user_id query string true "操作用户ID"
// @Success 200 {object} BaseResponse "移除成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 403 {object} ErrorResponse "权限不足"
// @Failure 404 {object} ErrorResponse "家庭组或设备不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/family-groups/{group_id}/devices/{device_id} [delete]
func (h *Handler) RemoveFamilyGroupDevice(c *gin.Context) {
	groupID := c.Param("group_id")
	deviceID := c.Param("device_id")
	userID := c.Query("user_id")

	if groupID == "" || deviceID == "" || userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "group_id, device_id, and user_id are required"})
		return
	}

	if err := h.service.RemoveFamilyGroupDevice(userID, groupID, deviceID); err != nil {
		statusCode := http.StatusInternalServerError
		if strings.Contains(err.Error(), "无权移除") || strings.Contains(err.Error(), "没有权限") {
			statusCode = http.StatusForbidden
		} else if strings.Contains(err.Error(), "不存在") || strings.Contains(err.Error(), "不在该家庭组中") {
			statusCode = http.StatusNotFound
		}
		c.JSON(statusCode, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, &BaseResponse{
		Status:  "success",
		Message: "从家庭组移除设备成功",
	})
}

// ListFamilyGroupMembers godoc
// @Summary 获取家庭组成员列表
// @Description 获取指定家庭组的所有成员列表
// @Tags 家庭组
// @Accept json
// @Produce json
// @Param group_id path string true "家庭组ID"
// @Param user_id query string true "操作用户ID"
// @Success 200 {object} BaseResponse{data=[]FamilyGroupMember} "获取成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 403 {object} ErrorResponse "权限不足"
// @Failure 404 {object} ErrorResponse "家庭组不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/family-groups/{group_id}/members [get]
func (h *Handler) ListFamilyGroupMembers(c *gin.Context) {
	groupID := c.Param("group_id")
	userID := c.Query("user_id")

	if groupID == "" || userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "group_id and user_id are required"})
		return
	}

	members, err := h.service.ListFamilyGroupMembers(userID, groupID)
	if err != nil {
		statusCode := http.StatusInternalServerError
		if strings.Contains(err.Error(), "不是该家庭组的成员") {
			statusCode = http.StatusForbidden
		} else if strings.Contains(err.Error(), "不存在") {
			statusCode = http.StatusNotFound
		}
		c.JSON(statusCode, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, &BaseResponse{
		Status:  "success",
		Message: "获取家庭组成员列表成功",
		Data:    members,
	})
}

// ListFamilyGroupDevices godoc
// @Summary 获取家庭组设备列表
// @Description 获取指定家庭组的所有设备列表
// @Tags 家庭组
// @Accept json
// @Produce json
// @Param group_id path string true "家庭组ID"
// @Param user_id query string true "操作用户ID"
// @Success 200 {object} BaseResponse{data=[]FamilyGroupDevice} "获取成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 403 {object} ErrorResponse "权限不足"
// @Failure 404 {object} ErrorResponse "家庭组不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/family-groups/{group_id}/devices [get]
func (h *Handler) ListFamilyGroupDevices(c *gin.Context) {
	groupID := c.Param("group_id")
	userID := c.Query("user_id")

	if groupID == "" || userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "group_id and user_id are required"})
		return
	}

	devices, err := h.service.ListFamilyGroupDevices(userID, groupID)
	if err != nil {
		statusCode := http.StatusInternalServerError
		if strings.Contains(err.Error(), "不是该家庭组的成员") {
			statusCode = http.StatusForbidden
		} else if strings.Contains(err.Error(), "不存在") {
			statusCode = http.StatusNotFound
		}
		c.JSON(statusCode, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, &BaseResponse{
		Status:  "success",
		Message: "获取家庭组设备列表成功",
		Data:    devices,
	})
}

// ListUserAccessibleDevices godoc
// @Summary 获取用户可访问的所有设备
// @Description 获取用户可访问的设备列表，包括自己的设备和家庭组中的设备
// @Tags 设备管理
// @Accept json
// @Produce json
// @Param user_id query string true "用户ID"
// @Success 200 {object} BaseResponse{data=[]Device} "获取成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/devices/accessible [get]
func (h *Handler) ListUserAccessibleDevices(c *gin.Context) {
	userID := c.Query("user_id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "user_id is required"})
		return
	}

	devices, err := h.service.ListUserAccessibleDevices(userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, &BaseResponse{
		Status:  "success",
		Message: "获取用户可访问设备列表成功",
		Data:    devices,
	})
}

// CreateFamilyGroupInvitation godoc
// @Summary 创建家庭组邀请
// @Description 向指定用户发送家庭组邀请，需要管理员或拥有者权限
// @Tags 家庭组邀请
// @Accept json
// @Produce json
// @Param group_id path string true "家庭组ID"
// @Param user_id query string true "操作用户ID"
// @Param request body FamilyGroupInvitationCreateRequest true "邀请信息"
// @Success 200 {object} BaseResponse{data=FamilyGroupInvitation} "创建成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 403 {object} ErrorResponse "权限不足"
// @Failure 404 {object} ErrorResponse "家庭组或用户不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/family-groups/{group_id}/invitations [post]
func (h *Handler) CreateFamilyGroupInvitation(c *gin.Context) {
	groupID := c.Param("group_id")
	userID := c.Query("user_id")

	if groupID == "" || userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "group_id and user_id are required"})
		return
	}

	var req FamilyGroupInvitationCreateRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request format"})
		return
	}

	invitation, err := h.service.CreateFamilyGroupInvitation(userID, groupID, &req)
	if err != nil {
		statusCode := http.StatusInternalServerError
		if strings.Contains(err.Error(), "无权发送") || strings.Contains(err.Error(), "没有权限") {
			statusCode = http.StatusForbidden
		} else if strings.Contains(err.Error(), "不存在") {
			statusCode = http.StatusNotFound
		} else if strings.Contains(err.Error(), "已存在") || strings.Contains(err.Error(), "已达上限") {
			statusCode = http.StatusBadRequest
		}
		c.JSON(statusCode, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, &BaseResponse{
		Status:  "success",
		Message: "发送家庭组邀请成功",
		Data:    invitation,
	})
}

// CreateFamilyGroupInvitationByEmail godoc
// @Summary 通过邮箱创建家庭组邀请
// @Description 向指定邮箱的用户发送家庭组邀请，需要管理员或拥有者权限
// @Tags 家庭组邀请
// @Accept json
// @Produce json
// @Param group_id path string true "家庭组ID"
// @Param user_id query string true "操作用户ID"
// @Param request body FamilyGroupInvitationCreateByEmailRequest true "邀请信息"
// @Success 200 {object} BaseResponse{data=FamilyGroupInvitation} "创建成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 403 {object} ErrorResponse "权限不足"
// @Failure 404 {object} ErrorResponse "家庭组或用户不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/family-groups/{group_id}/invitations/email [post]
func (h *Handler) CreateFamilyGroupInvitationByEmail(c *gin.Context) {
	groupID := c.Param("group_id")
	userID := c.Query("user_id")

	if groupID == "" || userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "group_id and user_id are required"})
		return
	}

	var req FamilyGroupInvitationCreateByEmailRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request format"})
		return
	}

	if req.InviteeEmail == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invitee_email is required"})
		return
	}

	invitation, err := h.service.CreateFamilyGroupInvitationByEmail(userID, groupID, &req)
	if err != nil {
		statusCode := http.StatusInternalServerError
		if strings.Contains(err.Error(), "无权发送") || strings.Contains(err.Error(), "没有权限") {
			statusCode = http.StatusForbidden
		} else if strings.Contains(err.Error(), "不存在") {
			statusCode = http.StatusNotFound
		} else if strings.Contains(err.Error(), "已存在") || strings.Contains(err.Error(), "已达上限") {
			statusCode = http.StatusBadRequest
		}
		c.JSON(statusCode, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, &BaseResponse{
		Status:  "success",
		Message: "发送家庭组邀请成功",
		Data:    invitation,
	})
}

// GetFamilyGroupInvitation godoc
// @Summary 获取家庭组邀请详情
// @Description 获取指定邀请的详细信息，仅邀请的发送者和接收者可查看
// @Tags 家庭组邀请
// @Accept json
// @Produce json
// @Param invitation_id path string true "邀请ID"
// @Param user_id query string true "操作用户ID"
// @Success 200 {object} BaseResponse{data=FamilyGroupInvitation} "获取成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 403 {object} ErrorResponse "权限不足"
// @Failure 404 {object} ErrorResponse "邀请不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/family-groups/invitations/{invitation_id} [get]
func (h *Handler) GetFamilyGroupInvitation(c *gin.Context) {
	invitationID := c.Param("invitation_id")
	userID := c.Query("user_id")

	if invitationID == "" || userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invitation_id and user_id are required"})
		return
	}

	invitation, err := h.service.GetFamilyGroupInvitation(userID, invitationID)
	if err != nil {
		statusCode := http.StatusInternalServerError
		if strings.Contains(err.Error(), "没有权限") {
			statusCode = http.StatusForbidden
		} else if strings.Contains(err.Error(), "不存在") {
			statusCode = http.StatusNotFound
		}
		c.JSON(statusCode, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, &BaseResponse{
		Status:  "success",
		Message: "获取家庭组邀请成功",
		Data:    invitation,
	})
}

// ListReceivedFamilyGroupInvitations godoc
// @Summary 获取收到的家庭组邀请
// @Description 获取当前用户收到的所有待处理家庭组邀请
// @Tags 家庭组邀请
// @Accept json
// @Produce json
// @Param user_id query string true "用户ID"
// @Success 200 {object} BaseResponse{data=[]FamilyGroupInvitationResponse} "获取成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/family-groups/invitations/received [get]
func (h *Handler) ListReceivedFamilyGroupInvitations(c *gin.Context) {
	userID := c.Query("user_id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "user_id is required"})
		return
	}

	invitations, err := h.service.ListReceivedFamilyGroupInvitations(userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, &BaseResponse{
		Status:  "success",
		Message: "获取收到的家庭组邀请成功",
		Data:    invitations,
	})
}

// ListSentFamilyGroupInvitations godoc
// @Summary 获取发送的家庭组邀请
// @Description 获取当前用户发送的所有家庭组邀请
// @Tags 家庭组邀请
// @Accept json
// @Produce json
// @Param user_id query string true "用户ID"
// @Success 200 {object} BaseResponse{data=[]FamilyGroupInvitationResponse} "获取成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/family-groups/invitations/sent [get]
func (h *Handler) ListSentFamilyGroupInvitations(c *gin.Context) {
	userID := c.Query("user_id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "user_id is required"})
		return
	}

	invitations, err := h.service.ListSentFamilyGroupInvitations(userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, &BaseResponse{
		Status:  "success",
		Message: "获取发送的家庭组邀请成功",
		Data:    invitations,
	})
}

// ProcessFamilyGroupInvitation godoc
// @Summary 处理家庭组邀请
// @Description 接受或拒绝收到的家庭组邀请
// @Tags 家庭组邀请
// @Accept json
// @Produce json
// @Param invitation_id path string true "邀请ID"
// @Param user_id query string true "操作用户ID"
// @Param action query string true "操作类型: accept或reject"
// @Success 200 {object} BaseResponse "处理成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 403 {object} ErrorResponse "权限不足"
// @Failure 404 {object} ErrorResponse "邀请不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/family-groups/invitations/{invitation_id}/process [put]
func (h *Handler) ProcessFamilyGroupInvitation(c *gin.Context) {
	invitationID := c.Param("invitation_id")
	userID := c.Query("user_id")
	action := c.Query("action")

	if invitationID == "" || userID == "" || action == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invitation_id, user_id and action are required"})
		return
	}

	// 检查action参数
	var accept bool
	if action == "accept" {
		accept = true
	} else if action == "reject" {
		accept = false
	} else {
		c.JSON(http.StatusBadRequest, gin.H{"error": "action must be either 'accept' or 'reject'"})
		return
	}

	err := h.service.ProcessFamilyGroupInvitation(userID, invitationID, accept)
	if err != nil {
		statusCode := http.StatusInternalServerError
		if strings.Contains(err.Error(), "不是此邀请的接收者") {
			statusCode = http.StatusForbidden
		} else if strings.Contains(err.Error(), "不存在") {
			statusCode = http.StatusNotFound
		} else if strings.Contains(err.Error(), "已经被处理") || strings.Contains(err.Error(), "已过期") {
			statusCode = http.StatusBadRequest
		}
		c.JSON(statusCode, gin.H{"error": err.Error()})
		return
	}

	message := "拒绝家庭组邀请成功"
	if accept {
		message = "接受家庭组邀请成功"
	}

	c.JSON(http.StatusOK, &BaseResponse{
		Status:  "success",
		Message: message,
	})
}

// CancelFamilyGroupInvitation godoc
// @Summary 取消家庭组邀请
// @Description 取消发送的家庭组邀请，仅邀请者或组管理员可操作
// @Tags 家庭组邀请
// @Accept json
// @Produce json
// @Param invitation_id path string true "邀请ID"
// @Param user_id query string true "操作用户ID"
// @Success 200 {object} BaseResponse "取消成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 403 {object} ErrorResponse "权限不足"
// @Failure 404 {object} ErrorResponse "邀请不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/family-groups/invitations/{invitation_id}/cancel [delete]
func (h *Handler) CancelFamilyGroupInvitation(c *gin.Context) {
	userID := c.GetString("user_id")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "认证失败"})
		return
	}

	invitationID := c.Param("invitation_id")
	if invitationID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "邀请ID不能为空"})
		return
	}

	if err := h.service.CancelFamilyGroupInvitation(userID, invitationID); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "邀请已取消",
	})
}

// ==================== 设备OTA状态API ====================

// GetDeviceOTAStatus godoc
// @Summary 获取设备OTA状态
// @Description App端获取指定设备的OTA状态
// @Tags 设备管理
// @Accept json
// @Produce json
// @Param device_id path string true "设备ID"
// @Success 200 {object} DeviceOTAStatusResponse "OTA状态信息"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 404 {object} ErrorResponse "设备不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/devices/{device_id}/ota-status [get]
func (h *Handler) GetDeviceOTAStatus(c *gin.Context) {
	deviceID := c.Param("device_id")
	if deviceID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "设备ID不能为空"})
		return
	}

	// 验证用户对设备的访问权限
	userID := c.GetString("user_id")
	if userID != "" {
		hasAccess, err := h.service.HasUserDeviceAccess(userID, deviceID)
		if err != nil || !hasAccess {
			c.JSON(http.StatusForbidden, gin.H{"error": "无权限访问此设备"})
			return
		}
	}

	status, err := h.service.GetDeviceOTAStatus(deviceID)
	if err != nil {
		if err.Error() == "设备不存在" {
			c.JSON(http.StatusNotFound, gin.H{"error": "设备不存在"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		}
		return
	}

	c.JSON(http.StatusOK, status)
}

// UpdateDeviceOTAStatus godoc
// @Summary 更新设备OTA状态
// @Description 设备端更新OTA状态
// @Tags 设备管理
// @Accept json
// @Produce json
// @Param device_id path string true "设备ID"
// @Param request body DeviceOTAStatusRequest true "OTA状态信息"
// @Success 200 {object} map[string]string "更新成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 404 {object} ErrorResponse "设备不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/devices/{device_id}/ota-status [put]
func (h *Handler) UpdateDeviceOTAStatus(c *gin.Context) {
	deviceID := c.Param("device_id")
	if deviceID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "设备ID不能为空"})
		return
	}

	var req DeviceOTAStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("请求参数错误: %v", err)})
		return
	}

	// 确保请求中的设备ID与路径参数一致
	if req.DeviceID != deviceID {
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求体中的设备ID与路径参数不一致"})
		return
	}

	if err := h.service.UpdateDeviceOTAStatus(deviceID, req.Status); err != nil {
		if strings.Contains(err.Error(), "设备不存在") {
			c.JSON(http.StatusNotFound, gin.H{"error": "设备不存在"})
		} else if strings.Contains(err.Error(), "无效的OTA状态值") {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "OTA状态更新成功",
	})
}

// GetAsset godoc
// @Summary 获取存储资源
// @Description 获取存储在assets中的文件（如头像等）
// @Tags 存储
// @Accept json
// @Produce octet-stream
// @Param filepath path string true "文件路径"
// @Success 200 "文件内容"
// @Failure 404 {object} ErrorResponse "文件不存在"
// @Failure 500 {object} ErrorResponse "服务器错误"
// @Router /api/storage/assets/{filepath} [get]
func (h *Handler) GetAsset(c *gin.Context) {
	filepath := c.Param("filepath")
	// 移除开头的斜杠
	filepath = strings.TrimPrefix(filepath, "/")

	// 检查是否是猫咪头像访问
	if strings.HasPrefix(filepath, "avatars/cats/") {
		// 获取用户ID（通过认证中间件设置）
		userID := c.GetString("user_id")
		if userID == "" {
			c.JSON(http.StatusUnauthorized, ErrorResponse{
				Code:    http.StatusUnauthorized,
				Message: "Authentication required",
				Error:   "User authentication required to access cat avatars",
			})
			return
		}

		// 从路径中提取猫咪ID
		// 路径格式: avatars/cats/{catID}.jpg
		pathParts := strings.Split(filepath, "/")
		if len(pathParts) == 3 {
			filename := pathParts[2]
			catID := strings.TrimSuffix(filename, ".jpg")

			// 检查用户是否拥有该猫咪
			hasOwnership, err := h.service.CheckUserCatOwnership(userID, catID)
			if err != nil {
				c.JSON(http.StatusNotFound, ErrorResponse{
					Code:    http.StatusNotFound,
					Message: "Cat not found",
					Error:   err.Error(),
				})
				return
			}

			if !hasOwnership {
				c.JSON(http.StatusForbidden, ErrorResponse{
					Code:    http.StatusForbidden,
					Message: "Access denied",
					Error:   "You can only access avatars of your own cats",
				})
				return
			}
		}
	}

	// 构建完整的assets路径
	fullPath := "assets/" + filepath

	// 获取文件对象
	obj, err := h.service.GetStorageObject(fullPath)
	if err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{
			Code:    http.StatusNotFound,
			Message: "File not found",
			Error:   err.Error(),
		})
		return
	}
	defer obj.Close()

	// 获取文件信息
	stat, err := obj.Stat()
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    http.StatusInternalServerError,
			Message: "Failed to get file info",
			Error:   err.Error(),
		})
		return
	}

	// 设置适当的Content-Type
	contentType := "application/octet-stream"
	if strings.HasSuffix(filepath, ".jpg") || strings.HasSuffix(filepath, ".jpeg") {
		contentType = "image/jpeg"
	} else if strings.HasSuffix(filepath, ".png") {
		contentType = "image/png"
	} else if strings.HasSuffix(filepath, ".gif") {
		contentType = "image/gif"
	}

	// 设置响应头
	c.Header("Content-Type", contentType)
	c.Header("Content-Length", fmt.Sprintf("%d", stat.Size))
	c.Header("Cache-Control", "public, max-age=3600") // 缓存1小时

	// 直接写入响应
	c.DataFromReader(http.StatusOK, stat.Size, contentType, obj, nil)
}

// ==================== 设备传感器状态API ====================

// ReportDeviceSensorError godoc
// @Summary 上报设备传感器错误
// @Description 设备端上报传感器错误信息，更新最后一次错误时间
// @Tags 设备管理
// @Accept json
// @Produce json
// @Param device_id path string true "设备ID"
// @Param request body DeviceSensorErrorRequest true "传感器错误信息"
// @Success 200 {object} BaseResponse "上报成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 404 {object} ErrorResponse "设备不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/devices/{device_id}/sensor-errors [post]
func (h *Handler) ReportDeviceSensorError(c *gin.Context) {
	deviceID := c.Param("device_id")
	if deviceID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "设备ID不能为空"})
		return
	}

	var req DeviceSensorErrorRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("请求参数错误: %v", err)})
		return
	}

	// 确保请求中的设备ID与路径参数一致
	if req.DeviceID != deviceID {
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求体中的设备ID与路径参数不一致"})
		return
	}

	if err := h.service.ReportDeviceSensorError(&req); err != nil {
		if strings.Contains(err.Error(), "设备不存在") {
			c.JSON(http.StatusNotFound, gin.H{"error": "设备不存在"})
		} else if strings.Contains(err.Error(), "不支持的传感器类型") {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "传感器错误信息上报成功",
	})
}

// GetDeviceSensorStatus godoc
// @Summary 获取设备传感器状态
// @Description 获取指定设备的传感器状态信息，包括各传感器最后一次错误时间
// @Tags 设备管理
// @Accept json
// @Produce json
// @Param device_id path string true "设备ID"
// @Param user_id query string false "用户ID"
// @Success 200 {object} DeviceSensorStatusResponse "传感器状态信息"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 403 {object} ErrorResponse "权限不足"
// @Failure 404 {object} ErrorResponse "设备不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/devices/{device_id}/sensor-status [get]
func (h *Handler) GetDeviceSensorStatus(c *gin.Context) {
	deviceID := c.Param("device_id")
	if deviceID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "设备ID不能为空"})
		return
	}

	userID := c.Query("user_id")

	// 检查用户权限（如果提供了用户ID）
	if userID != "" {
		err := h.service.CheckUserDeviceAccess(userID, deviceID)
		if err != nil {
			c.JSON(http.StatusForbidden, gin.H{"error": "没有权限访问该设备"})
			return
		}
	}

	status, err := h.service.GetDeviceSensorStatus(deviceID)
	if err != nil {
		if strings.Contains(err.Error(), "设备不存在") {
			c.JSON(http.StatusNotFound, gin.H{"error": "设备不存在"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		}
		return
	}

	c.JSON(http.StatusOK, status)
}

// ClearDeviceSensorError godoc
// @Summary 清除设备传感器错误状态
// @Description 清除指定设备的特定传感器错误状态
// @Tags 设备管理
// @Accept json
// @Produce json
// @Param device_id path string true "设备ID"
// @Param sensor_type path string true "传感器类型" Enums(camera,weight_sensor,temperature_humidity_sensor,microphone,wifi,bluetooth)
// @Param user_id query string false "用户ID"
// @Success 200 {object} BaseResponse "清除成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 403 {object} ErrorResponse "权限不足"
// @Failure 404 {object} ErrorResponse "设备不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/devices/{device_id}/sensor-errors/{sensor_type} [delete]
func (h *Handler) ClearDeviceSensorError(c *gin.Context) {
	deviceID := c.Param("device_id")
	sensorType := c.Param("sensor_type")

	if deviceID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "设备ID不能为空"})
		return
	}

	if sensorType == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "传感器类型不能为空"})
		return
	}

	userID := c.Query("user_id")

	// 检查用户权限（如果提供了用户ID）
	if userID != "" {
		err := h.service.CheckUserDeviceAccess(userID, deviceID)
		if err != nil {
			c.JSON(http.StatusForbidden, gin.H{"error": "没有权限访问该设备"})
			return
		}
	}

	if err := h.service.ClearDeviceSensorError(deviceID, sensorType); err != nil {
		if strings.Contains(err.Error(), "设备不存在") {
			c.JSON(http.StatusNotFound, gin.H{"error": "设备不存在"})
		} else if strings.Contains(err.Error(), "不支持的传感器类型") {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": fmt.Sprintf("已清除设备 %s 的 %s 传感器错误状态", deviceID, sensorType),
	})
}

// ListDevicesWithSensorErrors godoc
// @Summary 获取有传感器错误的设备列表
// @Description 获取所有存在传感器错误的设备列表（管理员接口）
// @Tags 设备管理
// @Accept json
// @Produce json
// @Success 200 {array} DeviceSensorStatus "设备传感器状态列表"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/devices/sensor-errors [get]
func (h *Handler) ListDevicesWithSensorErrors(c *gin.Context) {
	devices, err := h.service.ListDevicesWithSensorErrors()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, devices)
}
