package cattoilet

import (
	"cabycare-server/config"
	"cabycare-server/pkg/algo"
	"fmt"
	"log"
	"time"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

type Database struct {
	db *gorm.DB
}

// NewDatabase 创建数据库连接
func NewDatabase(cfg *config.Config) (*Database, error) {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		cfg.MySQL.User,
		cfg.MySQL.Password,
		cfg.MySQL.Host,
		cfg.MySQL.Port,
		cfg.MySQL.Database,
	)

	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %v", err)
	}

	return &Database{db: db}, nil
}

// ==================== 用户相关方法 ====================

func (d *Database) CreateUserProfile(profile *UserProfile) error {
	return d.db.Create(profile).Error
}

func (d *Database) UpdateUserProfile(profile *UserProfile) error {
	return d.db.Save(profile).Error
}

func (d *Database) GetUserProfile(userID string) (*UserProfile, error) {
	var profile UserProfile
	if err := d.db.Where("user_id = ?", userID).First(&profile).Error; err != nil {
		return nil, err
	}
	return &profile, nil
}

func (d *Database) UpdateUserSettings(settings *UserSettings) error {
	return d.db.Save(settings).Error
}

func (d *Database) GetUserSettings(userID string) (*UserSettings, error) {
	var settings UserSettings
	if err := d.db.Where("user_id = ?", userID).First(&settings).Error; err != nil {
		return nil, err
	}
	return &settings, nil
}

func (d *Database) CreateUserLog(log *UserLog) error {
	return d.db.Create(log).Error
}

// ==================== 设备相关方法 ====================

func (d *Database) UpdateDevice(device *Device) error {
	return d.db.Save(device).Error
}

func (d *Database) UpdateDeviceStatus(status *DeviceStatus) error {
	return d.db.Save(status).Error
}

func (d *Database) GetDeviceStatus(deviceID string) (*DeviceStatus, error) {
	var status DeviceStatus
	if err := d.db.Where("device_id = ?", deviceID).First(&status).Error; err != nil {
		return nil, err
	}
	return &status, nil
}

func (d *Database) UpdateDeviceConfig(config *DeviceConfig) error {
	return d.db.Save(config).Error
}

func (d *Database) GetDeviceConfig(deviceID string) (*DeviceConfig, error) {
	var config DeviceConfig
	if err := d.db.Where("device_id = ?", deviceID).First(&config).Error; err != nil {
		return nil, err
	}
	return &config, nil
}

func (d *Database) CreateDeviceMaintenance(maintenance *DeviceMaintenance) error {
	return d.db.Create(maintenance).Error
}

// ==================== 猫咪相关方法 ====================

func (d *Database) UpdateCat(cat *Cat) error {
	return d.db.Save(cat).Error
}

func (d *Database) UpdateCatBehavior(behavior *CatBehavior) error {
	return d.db.Save(behavior).Error
}

// ==================== 统计相关方法 ====================

func (d *Database) UpdateCatMetricsDaily(metrics *CatMetricsDaily) error {
	return d.db.Save(metrics).Error
}

func (d *Database) UpdateCatMetricsMonthly(metrics *CatMetricsMonthly) error {
	return d.db.Save(metrics).Error
}

func (d *Database) CreateCatAlert(alert *CatAlert) error {
	return d.db.Create(alert).Error
}

func (d *Database) CreateNotification(notification *Notification) error {
	return d.db.Create(notification).Error
}

// ==================== 查询相关方法 ====================

func (d *Database) ListCatAlerts(catID string, status int8) ([]CatAlert, error) {
	query := d.db.Where("cat_id = ?", catID)

	if status > 0 {
		query = query.Where("status = ?", status)
	}

	var alerts []CatAlert
	if err := query.Order("created_at DESC").Find(&alerts).Error; err != nil {
		return nil, err
	}
	return alerts, nil
}

func (d *Database) ListUserNotifications(userID string, isRead *bool) ([]Notification, error) {
	query := d.db.Where("user_id = ?", userID)

	if isRead != nil {
		query = query.Where("is_read = ?", *isRead)
	}

	var notifications []Notification
	if err := query.Order("created_at DESC").Find(&notifications).Error; err != nil {
		return nil, err
	}
	return notifications, nil
}

// ==================== 统计查询方法 ====================

func (d *Database) GetCatDailyMetrics(catID string, date time.Time) (*CatMetricsDaily, error) {
	var metrics CatMetricsDaily
	if err := d.db.Where("cat_id = ? AND metric_date = ?", catID, date).First(&metrics).Error; err != nil {
		return nil, err
	}
	return &metrics, nil
}

func (d *Database) GetCatMonthlyMetrics(catID string, year, month int) (*CatMetricsMonthly, error) {
	var metrics CatMetricsMonthly
	if err := d.db.Where("cat_id = ? AND year = ? AND month = ?", catID, year, month).First(&metrics).Error; err != nil {
		return nil, err
	}
	return &metrics, nil
}

// ==================== 事务方法 ====================

func (d *Database) Transaction(fn func(tx *gorm.DB) error) error {
	return d.db.Transaction(fn)
}

// User 相关方法
func (d *Database) CreateUser(user *User) error {
	return d.db.Create(user).Error
}

func (d *Database) GetUser(userID string) (*User, error) {
	var user User
	if err := d.db.Where("user_id = ?", userID).First(&user).Error; err != nil {
		return nil, err
	}
	return &user, nil
}

func (d *Database) GetUserByUsername(username string) (*User, error) {
	var user User
	if err := d.db.Where("username = ?", username).First(&user).Error; err != nil {
		return nil, err
	}
	return &user, nil
}

func (d *Database) UpdateUser(user *User) error {
	return d.db.Save(user).Error
}

// Device 相关方法
func (d *Database) CreateDevice(device *Device) error {
	return d.db.Create(device).Error
}

func (d *Database) GetDevice(deviceID string) (*Device, error) {
	var device Device
	if err := d.db.First(&device, "device_id = ?", deviceID).Error; err != nil {
		return nil, err
	}
	return &device, nil
}

func (d *Database) UpdateDeviceHeartbeat(deviceID string) error {
	return d.db.Model(&Device{}).
		Where("device_id = ?", deviceID).
		Update("last_heartbeat", time.Now()).Error
}

func (d *Database) UpdateDeviceTimezone(deviceID, timezone string) error {
	return d.db.Model(&Device{}).
		Where("device_id = ?", deviceID).
		Update("timezone", timezone).Error
}

func (d *Database) GetDeviceTimezone(deviceID string) (string, error) {
	var device Device
	if err := d.db.Where("device_id = ?", deviceID).First(&device).Error; err != nil {
		return "", err
	}
	return device.Timezone, nil
}

func (d *Database) ListUserDevices(userID string) ([]Device, error) {
	var devices []Device
	if err := d.db.Where("user_id = ?", userID).Find(&devices).Error; err != nil {
		return nil, err
	}
	return devices, nil
}

// Cat 相关方法
func (d *Database) CreateCat(cat *Cat) error {
	return d.db.Create(cat).Error
}

func (d *Database) GetCat(catID string) (*Cat, error) {
	var cat Cat
	if err := d.db.Where("cat_id = ?", catID).First(&cat).Error; err != nil {
		return nil, err
	}
	return &cat, nil
}

func (d *Database) ListUserCats(userID string) ([]Cat, error) {
	var cats []Cat
	if err := d.db.Where("user_id = ?", userID).Find(&cats).Error; err != nil {
		return nil, err
	}
	return cats, nil
}

func (d *Database) ListDeviceRecords(deviceID string, startTime, endTime *time.Time) ([]RecordShit, error) {
	query := d.db.Where("device_id = ?", deviceID)

	if startTime != nil {
		query = query.Where("start_time >= ?", startTime.Unix())
	}
	if endTime != nil {
		query = query.Where("start_time <= ?", endTime.Unix())
	}

	var records []RecordShit
	if err := query.Order("start_time DESC").Find(&records).Error; err != nil {
		return nil, err
	}
	return records, nil
}

// ListDeviceRecordsByUnixTime 通过Unix时间戳查询设备记录
func (d *Database) ListDeviceRecordsByUnixTime(deviceID string, startUnixTime, endUnixTime int64) ([]RecordShit, error) {
	// 1. 获取设备所属用户ID，用于构建videoID前缀
	var device Device
	if err := d.db.Where("device_id = ?", deviceID).First(&device).Error; err != nil {
		return nil, fmt.Errorf("找不到设备: %v", err)
	}

	// 2. 生成视频ID前缀（deviceHash + userHash，不包含时间戳部分）
	sampleVideoID, err := algo.GenerateVideoID(deviceID, device.UserID, 0)
	if err != nil {
		return nil, fmt.Errorf("生成视频ID前缀失败: %v", err)
	}
	videoIDPrefix := sampleVideoID[:16] // 固定长度的前缀（10位设备哈希 + 6位用户哈希）

	// 3. 生成时间戳范围的完整videoID
	var minVideoID, maxVideoID string
	if startUnixTime > 0 {
		minVideoID, err = algo.GenerateVideoID(deviceID, device.UserID, startUnixTime)
		if err != nil {
			return nil, fmt.Errorf("生成最小视频ID失败: %v", err)
		}
	} else {
		// 如果没有指定起始时间，使用前缀作为最小ID
		minVideoID = videoIDPrefix
	}

	if endUnixTime > 0 {
		maxVideoID, err = algo.GenerateVideoID(deviceID, device.UserID, endUnixTime)
		if err != nil {
			return nil, fmt.Errorf("生成最大视频ID失败: %v", err)
		}
	} else {
		// 如果没有指定结束时间，使用前缀+'z'重复（确保大于所有可能的时间戳十六进制）
		maxVideoID = videoIDPrefix + "zzzzzzzzzzzzzzzzzz" // 足够大的值覆盖任何可能的时间戳
	}

	// 4. 构建高效查询：直接使用videoID范围查询，明确只查询status=1（正常）的记录
	query := d.db.Where("video_id >= ? AND video_id <= ? AND status = 1 AND device_id = ?",
		minVideoID, maxVideoID, deviceID)

	// 5. 执行查询，按videoID降序排序（等效于按时间降序，但利用主键索引）
	var records []RecordShit
	if err := query.Order("video_id DESC").Find(&records).Error; err != nil {
		return nil, fmt.Errorf("查询记录失败: %v", err)
	}

	// 6. 预加载分析结果（使用一次查询获取所有分析结果）
	if len(records) > 0 {
		var videoIDs []string
		for i := range records {
			videoIDs = append(videoIDs, records[i].VideoID)
		}

		// 一次性查询所有视频的分析结果
		var analyses []RecordAnalysis
		if err := d.db.Where("video_id IN ?", videoIDs).Find(&analyses).Error; err != nil {
			log.Printf("获取分析结果失败: %v", err)
		} else {
			// 将分析结果映射到对应的记录
			analysisMap := make(map[string]*RecordAnalysis)
			for i := range analyses {
				analysis := analyses[i]
				analysisMap[analysis.VideoID] = &analysis
			}

			// 关联分析结果到记录
			for i := range records {
				if analysis, ok := analysisMap[records[i].VideoID]; ok {
					records[i].Analysis = analysis
				}
			}
		}
	}

	return records, nil
}

func (d *Database) ListUserRecords(userID string, startTime, endTime *time.Time) ([]RecordShit, error) {
	query := d.db.Where("user_id = ?", userID)
	if startTime != nil {
		query = query.Where("start_time >= ?", startTime.Unix())
	}
	if endTime != nil {
		query = query.Where("end_time <= ?", endTime.Unix())
	}
	var records []RecordShit
	if err := query.Find(&records).Error; err != nil {
		return nil, err
	}
	return records, nil
}

func (d *Database) ListCatRecords(catID string) ([]RecordShit, error) {
	var records []RecordShit
	if err := d.db.Where("cat_id = ?", catID).
		Order("start_time DESC").
		Find(&records).Error; err != nil {
		return nil, err
	}
	return records, nil
}

// GetRecordAnalysis 获取视频分析结果
func (d *Database) GetRecordAnalysis(videoID string) (*RecordAnalysis, error) {
	var analysis RecordAnalysis
	if err := d.db.Where("video_id = ?", videoID).First(&analysis).Error; err != nil {
		return nil, err
	}
	return &analysis, nil
}

// ListDeviceMaintenance 获取设备维护记录
func (d *Database) ListDeviceMaintenance(deviceID string, startTime, endTime *time.Time) ([]DeviceMaintenance, error) {
	query := d.db.Where("device_id = ?", deviceID)

	if startTime != nil {
		query = query.Where("maintenance_time >= ?", startTime)
	}
	if endTime != nil {
		query = query.Where("maintenance_time <= ?", endTime)
	}

	var records []DeviceMaintenance
	if err := query.Find(&records).Error; err != nil {
		return nil, err
	}
	return records, nil
}

// GetDeviceBatteryHistory 获取设备电池电量历史记录
func (d *Database) GetDeviceBatteryHistory(deviceID string, startTime, endTime *time.Time) ([]DeviceStatus, error) {
	query := d.db.Where("device_id = ?", deviceID)

	if startTime != nil {
		query = query.Where("updated_at >= ?", startTime)
	}
	if endTime != nil {
		query = query.Where("updated_at <= ?", endTime)
	}

	var records []DeviceStatus
	if err := query.Find(&records).Error; err != nil {
		return nil, err
	}
	return records, nil
}

// CreateUserHardware 创建用户硬件关联
func (d *Database) CreateUserHardware(hardware *UserHardware) error {
	return d.db.Create(hardware).Error
}

// ListUserHardware 获取用户的硬件关联列表
func (d *Database) ListUserHardware(userID string) ([]UserHardware, error) {
	var hardware []UserHardware
	if err := d.db.Where("user_id = ?", userID).Find(&hardware).Error; err != nil {
		return nil, err
	}
	return hardware, nil
}

// GetHardwareUser 获取硬件关联的用户
func (d *Database) GetHardwareUser(hardwareSN string) (*HardwareUserResponse, error) {
	var hardware UserHardware
	if err := d.db.Where("hardware_sn = ? AND status = 1", hardwareSN).First(&hardware).Error; err != nil {
		return nil, err
	}

	response := &HardwareUserResponse{
		UserID:     hardware.UserID,
		HardwareSN: hardware.HardwareSN,
	}
	return response, nil
}

// CheckUserHardwareExists 检查用户和硬件关联是否存在
func (d *Database) CheckUserHardwareExists(userID string, hardwareSN string) (bool, error) {
	var count int64
	result := d.db.Model(&UserHardware{}).
		Where("user_id = ? AND hardware_sn = ?", userID, hardwareSN).
		Count(&count)

	if result.Error != nil {
		return false, result.Error
	}

	return count > 0, nil
}

// ListUsers 获取所有用户列表
func (d *Database) ListUsers() ([]User, error) {
	var users []User
	if err := d.db.Find(&users).Error; err != nil {
		return nil, err
	}
	return users, nil
}

// SaveClientToken 保存或更新客户端令牌
func (d *Database) SaveClientToken(token *ClientToken) error {
	return d.db.Transaction(func(tx *gorm.DB) error {
		// 检查客户端是否存在
		var client Client
		if err := tx.Where("client_id = ?", token.ClientID).First(&client).Error; err != nil {
			return fmt.Errorf("client not found: %v", err)
		}

		// 检查令牌是否存在
		var existingToken ClientToken
		err := tx.Where("client_id = ?", token.ClientID).First(&existingToken).Error
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				// 令牌不存在，创建新记录
				return tx.Create(token).Error
			}
			return fmt.Errorf("failed to check existing token: %v", err)
		}

		// 令牌存在，更新记录
		updates := map[string]interface{}{
			"user_id":      token.UserID,
			"client_token": token.ClientToken,
			"token_type":   token.TokenType,
			"is_sandbox":   token.IsSandbox,
			"status":       token.Status,
		}
		return tx.Model(&existingToken).Updates(updates).Error
	})
}

// GetClientTokens 获取用户的所有客户端令牌
func (d *Database) GetClientTokens(userID string) ([]ClientToken, error) {
	var tokens []ClientToken
	err := d.db.Where("user_id = ?", userID).Find(&tokens).Error
	return tokens, err
}

// SaveClient 保存或更新客户端信息
func (d *Database) SaveClient(client *Client) error {
	return d.db.Save(client).Error
}

// GetClient 获取客户端信息
func (d *Database) GetClient(clientID string) (*Client, error) {
	var client Client
	err := d.db.Where("client_id = ?", clientID).First(&client).Error
	if err != nil {
		return nil, err
	}
	return &client, nil
}

// ListUserClients 获取用户的所有客户端
func (d *Database) ListUserClients(userID string) ([]Client, error) {
	var clients []Client
	err := d.db.Where("user_id = ?", userID).Find(&clients).Error
	return clients, err
}

// GetClientToken 获取客户端令牌信息
func (d *Database) GetClientToken(clientID string) (*ClientToken, error) {
	var token ClientToken
	err := d.db.Where("client_id = ?", clientID).First(&token).Error
	if err != nil {
		return nil, err
	}
	return &token, nil
}

// DeleteClientToken 删除客户端令牌
func (d *Database) DeleteClientToken(clientID string) error {
	return d.db.Where("client_id = ?", clientID).Delete(&ClientToken{}).Error
}

// UpdateClientStatus 更新客户端状态
func (d *Database) UpdateClientStatus(clientID string, status int8, appVersion string) error {
	updates := map[string]interface{}{
		"status":      status,
		"last_active": time.Now(),
	}
	if appVersion != "" {
		updates["app_version"] = appVersion
	}
	return d.db.Model(&Client{}).Where("client_id = ?", clientID).Updates(updates).Error
}

// DeleteClient 删除客户端
func (d *Database) DeleteClient(clientID string) error {
	return d.db.Transaction(func(tx *gorm.DB) error {
		// 先删除关联的令牌
		if err := tx.Where("client_id = ?", clientID).Delete(&ClientToken{}).Error; err != nil {
			return err
		}
		// 再删除客户端
		return tx.Where("client_id = ?", clientID).Delete(&Client{}).Error
	})
}

// GetUserByLogtoID 通过 Logto ID 获取用户
func (d *Database) GetUserByLogtoID(logtoID string) (*User, error) {
	var user User
	if err := d.db.Where("logto_id = ?", logtoID).First(&user).Error; err != nil {
		return nil, err
	}
	return &user, nil
}

// UpdateRecord 更新视频记录
func (d *Database) UpdateRecord(record *RecordShit) error {
	return d.db.Save(record).Error
}

// CreateRecordAnalysis 创建视频分析记录
func (d *Database) CreateRecordAnalysis(analysis *RecordAnalysis) error {
	return d.db.Create(analysis).Error
}

// GetRecordShit 获取视频记录
func (d *Database) GetRecordShit(videoID string) (*RecordShit, error) {
	var record RecordShit
	if err := d.db.Where("video_id = ?", videoID).First(&record).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			// Return an empty record with the requested videoID if not found
			return &RecordShit{
				VideoID: videoID,
			}, err
		}
		return nil, err
	}
	return &record, nil
}

// CreateShitRecord 创建视频记录
func (d *Database) CreateShitRecord(record *RecordShit) error {
	return d.db.Create(record).Error
}

func (d *Database) GetNotificationSettings(userID string) (*NotificationSettings, error) {
	var settings NotificationSettings
	result := d.db.Where("user_id = ?", userID).First(&settings)
	if result.Error != nil {
		return nil, result.Error
	}
	return &settings, nil
}

func (d *Database) CreateNotificationSettings(settings *NotificationSettings) error {
	return d.db.Create(settings).Error
}

// UpdateRecordAnalysis 更新视频分析记录
func (db *Database) UpdateRecordAnalysis(analysis *RecordAnalysis) error {
	updateData := map[string]interface{}{
		"animal_id":      analysis.AnimalID,
		"cat_confidence": analysis.CatConfidence,
		"behavior_type":  analysis.BehaviorType,
		"is_abnormal":    analysis.IsAbnormal,
		"abnormal_type":  analysis.AbnormalType,
		"abnormal_prob":  analysis.AbnormalProb,
		"updated_at":     analysis.UpdatedAt,
	}

	// 添加shadow模式相关字段（只保留重要信息）
	if analysis.ShadowSimilarity != nil {
		updateData["shadow_similarity"] = *analysis.ShadowSimilarity
	}
	if analysis.ShadowMatchedCatID != nil {
		updateData["shadow_matched_cat_id"] = *analysis.ShadowMatchedCatID
	}
	if analysis.ShadowModelVersion != nil {
		updateData["shadow_model_version"] = *analysis.ShadowModelVersion
	}

	return db.db.Model(&RecordAnalysis{}).
		Where("video_id = ?", analysis.VideoID).
		Updates(updateData).Error
}

// ListAllRecords 获取所有视频记录
func (d *Database) ListAllRecords() ([]RecordShit, error) {
	var records []RecordShit
	if err := d.db.Where("status = ?", 1).Find(&records).Error; err != nil {
		return nil, err
	}
	return records, nil
}

// SaveDeviceStatusRequest 处理设备状态请求并更新设备状态
func (d *Database) SaveDeviceStatusRequest(req *DeviceStatusRequest) error {
	return d.db.Transaction(func(tx *gorm.DB) error {
		// 1. 更新设备的最后心跳时间
		if err := tx.Model(&Device{}).
			Where("device_id = ?", req.DeviceID).
			Update("last_heartbeat", time.Now()).Error; err != nil {
			return fmt.Errorf("failed to update device heartbeat: %v", err)
		}

		// 2. 查找或创建设备状态记录
		var status DeviceStatus
		if err := tx.Where("device_id = ?", req.DeviceID).First(&status).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				// 创建设备状态记录
				status = DeviceStatus{
					DeviceID: req.DeviceID,
					Online:   true,
					IPv4:     req.IPv4,
					IPv6:     req.IPv6,
				}
				if err := tx.Create(&status).Error; err != nil {
					return fmt.Errorf("failed to create device status: %v", err)
				}
			} else {
				return fmt.Errorf("failed to query device status: %v", err)
			}
		} else {
			// 更新设备状态
			status.Online = true
			status.IPv4 = req.IPv4
			status.IPv6 = req.IPv6
			if err := tx.Save(&status).Error; err != nil {
				return fmt.Errorf("failed to update device status: %v", err)
			}
		}

		// 3. 创建设备状态历史记录
		history := DeviceStatusHistory{
			DeviceID:       req.DeviceID,
			Online:         true,
			SignalStrength: req.SignalStrength,
			StorageUsage:   req.StorageUsage,
			IPv4:           req.IPv4,
			IPv6:           req.IPv6,
		}
		if err := tx.Create(&history).Error; err != nil {
			return fmt.Errorf("failed to create device status history: %v", err)
		}

		return nil
	})
}

// GetDeviceStatusStatistics 获取设备状态统计数据
func (d *Database) GetDeviceStatusStatistics(deviceID string) (*DeviceStatusStatistics, error) {
	var stats DeviceStatusStatistics
	err := d.db.Where("device_id = ?", deviceID).First(&stats).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 如果不存在，创建空记录
			stats = DeviceStatusStatistics{
				DeviceID:          deviceID,
				OnlineRate:        0,
				PowerSupplyRate:   0,
				SignalStrengthAvg: 0,
				StorageUsage:      0,
			}
			if createErr := d.db.Create(&stats).Error; createErr != nil {
				return nil, fmt.Errorf("failed to create device statistics: %v", createErr)
			}
			return &stats, nil
		}
		return nil, err
	}
	return &stats, nil
}

// UpdateDeviceStatusStatistics 更新设备状态统计数据
func (d *Database) UpdateDeviceStatusStatistics(deviceID string, stats *DeviceStatusStatistics) error {
	return d.db.Save(stats).Error
}

// GetDeviceStatusHistory 获取设备的状态历史数据
func (d *Database) GetDeviceStatusHistory(deviceID string, startTime, endTime time.Time) ([]DeviceStatusHistory, error) {
	var history []DeviceStatusHistory
	query := d.db.Where("device_id = ?", deviceID)

	// 添加时间范围
	query = query.Where("created_at >= ? AND created_at <= ?", startTime, endTime)

	// 执行查询并按时间排序
	if err := query.Order("created_at ASC").Find(&history).Error; err != nil {
		return nil, err
	}

	return history, nil
}

// DeleteOldDeviceStatusHistory 删除指定时间前的设备状态历史数据
func (d *Database) DeleteOldDeviceStatusHistory(before time.Time) error {
	return d.db.Where("created_at < ?", before).Delete(&DeviceStatusHistory{}).Error
}

// CheckDeviceOnlineStatus 检查设备是否在线（基于最后心跳时间和超时设置）
func (d *Database) CheckDeviceOnlineStatus(deviceID string, timeoutMinutes int) (bool, error) {
	var device Device
	if err := d.db.Select("last_heartbeat").Where("device_id = ?", deviceID).First(&device).Error; err != nil {
		return false, err
	}

	// 如果没有最后心跳记录，设备离线
	if device.LastHeartbeat == nil {
		return false, nil
	}

	// 检查最后心跳时间是否超过超时时间
	timeoutDuration := time.Duration(timeoutMinutes) * time.Minute
	return time.Since(*device.LastHeartbeat) <= timeoutDuration, nil
}

// IsDeviceOnline 根据心跳超时时间判断设备是否在线
func (d *Database) IsDeviceOnline(deviceID string, heartbeatTimeout time.Duration) (bool, error) {
	var device Device
	err := d.db.Select("last_heartbeat").Where("device_id = ?", deviceID).First(&device).Error
	if err != nil {
		return false, err
	}

	if device.LastHeartbeat == nil {
		return false, nil
	}

	// 检查最后心跳时间是否在允许的超时范围内
	return time.Since(*device.LastHeartbeat) <= heartbeatTimeout, nil
}

// DeviceStatusHistory 设备状态历史记录
type DeviceStatusHistory struct {
	ID             int64     `gorm:"primaryKey;autoIncrement" json:"id"`
	DeviceID       string    `gorm:"column:device_id" json:"device_id"`
	Online         bool      `json:"online"`
	SignalStrength int8      `json:"signal_strength"`
	StorageUsage   int8      `json:"storage_usage"`
	IPv4           string    `json:"ipv4"`
	IPv6           string    `json:"ipv6"`
	CreatedAt      time.Time `gorm:"autoCreateTime" json:"created_at"`
}

// GetDeviceAutoOTASettings 获取设备自动OTA更新设置
func (d *Database) GetDeviceAutoOTASettings(deviceID string) (*DeviceAllSettings, error) {
	var settings DeviceAllSettings
	err := d.db.Table("device_all_settings").Where("device_id = ?", deviceID).First(&settings).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 如果不存在，创建默认设置（关闭自动更新，默认闲时时间2-4点）
			settings = DeviceAllSettings{
				DeviceID:            deviceID,
				AutoOTAUpgrade:      "off",
				IdleUpdateStartHour: 2,
				IdleUpdateEndHour:   4,
			}
			if err := d.db.Table("device_all_settings").Create(&settings).Error; err != nil {
				return nil, fmt.Errorf("failed to create device settings: %v", err)
			}
			return &settings, nil
		}
		return nil, fmt.Errorf("failed to query device settings: %v", err)
	}
	return &settings, nil
}

// UpdateDeviceAutoOTASettings 更新设备自动OTA更新设置
func (d *Database) UpdateDeviceAutoOTASettings(deviceID string, autoOTAUpgrade string) error {
	var settings DeviceAllSettings
	err := d.db.Table("device_all_settings").Where("device_id = ?", deviceID).First(&settings).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 如果不存在，创建新的设置
			settings = DeviceAllSettings{
				DeviceID:            deviceID,
				AutoOTAUpgrade:      autoOTAUpgrade,
				IdleUpdateStartHour: 2,
				IdleUpdateEndHour:   4,
			}
			if err := d.db.Table("device_all_settings").Create(&settings).Error; err != nil {
				return fmt.Errorf("failed to create device settings: %v", err)
			}
			return nil
		}
		return fmt.Errorf("failed to query device settings: %v", err)
	}

	// 更新设置
	settings.AutoOTAUpgrade = autoOTAUpgrade
	if err := d.db.Table("device_all_settings").Save(&settings).Error; err != nil {
		return fmt.Errorf("failed to update device settings: %v", err)
	}
	return nil
}

// UpdateDeviceIdleUpdateTime 更新设备闲时更新时间设置
func (d *Database) UpdateDeviceIdleUpdateTime(deviceID string, startHour, endHour int8) error {
	var settings DeviceAllSettings
	err := d.db.Table("device_all_settings").Where("device_id = ?", deviceID).First(&settings).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 如果不存在，创建新的设置
			settings = DeviceAllSettings{
				DeviceID:            deviceID,
				AutoOTAUpgrade:      "off",
				IdleUpdateStartHour: startHour,
				IdleUpdateEndHour:   endHour,
			}
			if err := d.db.Table("device_all_settings").Create(&settings).Error; err != nil {
				return fmt.Errorf("failed to create device settings: %v", err)
			}
			return nil
		}
		return fmt.Errorf("failed to query device settings: %v", err)
	}

	// 更新闲时时间设置
	settings.IdleUpdateStartHour = startHour
	settings.IdleUpdateEndHour = endHour
	if err := d.db.Table("device_all_settings").Save(&settings).Error; err != nil {
		return fmt.Errorf("failed to update device idle time settings: %v", err)
	}
	return nil
}

// UpdateDeviceAllSettings 更新设备所有设置
func (d *Database) UpdateDeviceAllSettings(deviceID string, settings *DeviceAllSettings) error {
	var existingSettings DeviceAllSettings
	err := d.db.Table("device_all_settings").Where("device_id = ?", deviceID).First(&existingSettings).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 如果不存在，创建新的设置
			settings.DeviceID = deviceID
			if err := d.db.Table("device_all_settings").Create(settings).Error; err != nil {
				return fmt.Errorf("failed to create device settings: %v", err)
			}
			return nil
		}
		return fmt.Errorf("failed to query device settings: %v", err)
	}

	// 更新所有设置
	if err := d.db.Table("device_all_settings").Save(settings).Error; err != nil {
		return fmt.Errorf("failed to update device settings: %v", err)
	}
	return nil
}

// ==================== 家庭组相关方法 ====================

// CreateFamilyGroup 创建家庭组
func (d *Database) CreateFamilyGroup(group *FamilyGroup) error {
	return d.db.Transaction(func(tx *gorm.DB) error {
		// 创建家庭组
		if err := tx.Create(group).Error; err != nil {
			return fmt.Errorf("创建家庭组失败: %v", err)
		}

		// 添加创建者作为拥有者成员
		member := &FamilyGroupMember{
			GroupID:  group.GroupID,
			UserID:   group.OwnerID,
			Nickname: "创建者", // 可以后续更新
			Role:     FamilyMemberRoleOwner,
		}
		if err := tx.Create(member).Error; err != nil {
			return fmt.Errorf("添加创建者为组成员失败: %v", err)
		}

		return nil
	})
}

// GetFamilyGroup 获取家庭组信息
func (d *Database) GetFamilyGroup(groupID string) (*FamilyGroup, error) {
	var group FamilyGroup
	if err := d.db.Where("group_id = ?", groupID).First(&group).Error; err != nil {
		return nil, err
	}
	return &group, nil
}

// UpdateFamilyGroup 更新家庭组信息
func (d *Database) UpdateFamilyGroup(group *FamilyGroup) error {
	return d.db.Save(group).Error
}

// DeleteFamilyGroup 删除家庭组
func (d *Database) DeleteFamilyGroup(groupID string) error {
	return d.db.Transaction(func(tx *gorm.DB) error {
		// 删除关联的设备
		if err := tx.Where("group_id = ?", groupID).Delete(&FamilyGroupDevice{}).Error; err != nil {
			return fmt.Errorf("删除家庭组设备关联失败: %v", err)
		}

		// 删除关联的成员
		if err := tx.Where("group_id = ?", groupID).Delete(&FamilyGroupMember{}).Error; err != nil {
			return fmt.Errorf("删除家庭组成员失败: %v", err)
		}

		// 删除家庭组
		if err := tx.Where("group_id = ?", groupID).Delete(&FamilyGroup{}).Error; err != nil {
			return fmt.Errorf("删除家庭组失败: %v", err)
		}

		return nil
	})
}

// ListUserFamilyGroups 获取用户所有的家庭组
func (d *Database) ListUserFamilyGroups(userID string) ([]FamilyGroup, error) {
	var groups []FamilyGroup

	// 查询用户所属的家庭组ID列表
	var memberRecords []FamilyGroupMember
	if err := d.db.Where("user_id = ?", userID).Find(&memberRecords).Error; err != nil {
		return nil, fmt.Errorf("查询用户所属家庭组成员记录失败: %v", err)
	}

	if len(memberRecords) == 0 {
		return []FamilyGroup{}, nil
	}

	// 提取家庭组ID列表
	var groupIDs []string
	for _, member := range memberRecords {
		groupIDs = append(groupIDs, member.GroupID)
	}

	// 查询这些家庭组的详细信息
	if err := d.db.Where("group_id IN ?", groupIDs).Find(&groups).Error; err != nil {
		return nil, fmt.Errorf("查询家庭组信息失败: %v", err)
	}

	return groups, nil
}

// ListUserOwnedFamilyGroups 获取用户创建的家庭组
func (d *Database) ListUserOwnedFamilyGroups(userID string) ([]FamilyGroup, error) {
	var groups []FamilyGroup
	if err := d.db.Where("owner_id = ?", userID).Find(&groups).Error; err != nil {
		return nil, err
	}
	return groups, nil
}

// AddFamilyGroupMember 添加家庭组成员
func (d *Database) AddFamilyGroupMember(member *FamilyGroupMember) error {
	// 检查用户是否已经是该组成员
	var count int64
	if err := d.db.Model(&FamilyGroupMember{}).
		Where("group_id = ? AND user_id = ?", member.GroupID, member.UserID).
		Count(&count).Error; err != nil {
		return fmt.Errorf("检查用户是否已是组成员时出错: %v", err)
	}

	if count > 0 {
		return fmt.Errorf("该用户已经是家庭组成员")
	}

	// 检查组成员数量是否达到上限
	var group FamilyGroup
	if err := d.db.Where("group_id = ?", member.GroupID).First(&group).Error; err != nil {
		return fmt.Errorf("获取家庭组信息失败: %v", err)
	}

	var memberCount int64
	if err := d.db.Model(&FamilyGroupMember{}).
		Where("group_id = ?", member.GroupID).
		Count(&memberCount).Error; err != nil {
		return fmt.Errorf("获取家庭组成员数量失败: %v", err)
	}

	if int(memberCount) >= group.MaxMembers {
		return fmt.Errorf("家庭组成员数量已达上限 %d", group.MaxMembers)
	}

	// 添加成员
	return d.db.Create(member).Error
}

// UpdateFamilyGroupMember 更新家庭组成员
func (d *Database) UpdateFamilyGroupMember(member *FamilyGroupMember) error {
	return d.db.Model(&FamilyGroupMember{}).
		Where("group_id = ? AND user_id = ?", member.GroupID, member.UserID).
		Updates(map[string]interface{}{
			"nickname": member.Nickname,
			"role":     member.Role,
		}).Error
}

// RemoveFamilyGroupMember 移除家庭组成员
func (d *Database) RemoveFamilyGroupMember(groupID, userID string) error {
	// 检查是否为拥有者
	var member FamilyGroupMember
	if err := d.db.Where("group_id = ? AND user_id = ?", groupID, userID).First(&member).Error; err != nil {
		return fmt.Errorf("获取家庭组成员失败: %v", err)
	}

	if member.Role == FamilyMemberRoleOwner {
		// 拥有者只能通过删除整个家庭组来移除自己
		return fmt.Errorf("不能移除家庭组的拥有者，如需退出请删除整个家庭组")
	}

	return d.db.Where("group_id = ? AND user_id = ?", groupID, userID).Delete(&FamilyGroupMember{}).Error
}

// GetFamilyGroupMember 获取家庭组成员信息
func (d *Database) GetFamilyGroupMember(groupID, userID string) (*FamilyGroupMember, error) {
	var member FamilyGroupMember
	if err := d.db.Where("group_id = ? AND user_id = ?", groupID, userID).First(&member).Error; err != nil {
		return nil, err
	}
	return &member, nil
}

// ListFamilyGroupMembers 获取家庭组所有成员
func (d *Database) ListFamilyGroupMembers(groupID string) ([]FamilyGroupMember, error) {
	var members []FamilyGroupMember
	if err := d.db.Where("group_id = ?", groupID).Find(&members).Error; err != nil {
		return nil, err
	}
	return members, nil
}

// AddFamilyGroupDevice 添加设备到家庭组
func (d *Database) AddFamilyGroupDevice(device *FamilyGroupDevice) error {
	// 检查设备是否已在该组
	var count int64
	if err := d.db.Model(&FamilyGroupDevice{}).
		Where("group_id = ? AND device_id = ?", device.GroupID, device.DeviceID).
		Count(&count).Error; err != nil {
		return fmt.Errorf("检查设备是否已在组内时出错: %v", err)
	}

	if count > 0 {
		return fmt.Errorf("该设备已经在家庭组中")
	}

	// 添加设备
	return d.db.Create(device).Error
}

// RemoveFamilyGroupDevice 从家庭组移除设备
func (d *Database) RemoveFamilyGroupDevice(groupID, deviceID string) error {
	return d.db.Where("group_id = ? AND device_id = ?", groupID, deviceID).Delete(&FamilyGroupDevice{}).Error
}

// ListFamilyGroupDevices 获取家庭组所有设备
func (d *Database) ListFamilyGroupDevices(groupID string) ([]FamilyGroupDevice, error) {
	var devices []FamilyGroupDevice
	if err := d.db.Where("group_id = ?", groupID).Find(&devices).Error; err != nil {
		return nil, err
	}
	return devices, nil
}

// GetFamilyGroupDevice 获取家庭组设备信息
func (d *Database) GetFamilyGroupDevice(groupID, deviceID string) (*FamilyGroupDevice, error) {
	var device FamilyGroupDevice
	if err := d.db.Where("group_id = ? AND device_id = ?", groupID, deviceID).First(&device).Error; err != nil {
		return nil, err
	}
	return &device, nil
}

// ListDeviceFamilyGroups 获取设备所属的所有家庭组
func (d *Database) ListDeviceFamilyGroups(deviceID string) ([]FamilyGroup, error) {
	var groups []FamilyGroup

	// 先查询设备所属的家庭组ID
	var deviceRecords []FamilyGroupDevice
	if err := d.db.Where("device_id = ?", deviceID).Find(&deviceRecords).Error; err != nil {
		return nil, err
	}

	if len(deviceRecords) == 0 {
		return []FamilyGroup{}, nil
	}

	// 提取家庭组ID
	var groupIDs []string
	for _, record := range deviceRecords {
		groupIDs = append(groupIDs, record.GroupID)
	}

	// 查询家庭组详情
	if err := d.db.Where("group_id IN ?", groupIDs).Find(&groups).Error; err != nil {
		return nil, err
	}

	return groups, nil
}

// GetFamilyGroupWithDetails 获取带详情的家庭组信息
func (d *Database) GetFamilyGroupWithDetails(groupID string) (*FamilyGroupWithDetails, error) {
	// 获取家庭组基本信息
	group, err := d.GetFamilyGroup(groupID)
	if err != nil {
		return nil, fmt.Errorf("获取家庭组信息失败: %v", err)
	}

	// 获取成员信息
	members, err := d.ListFamilyGroupMembers(groupID)
	if err != nil {
		return nil, fmt.Errorf("获取家庭组成员失败: %v", err)
	}

	// 获取设备信息
	devices, err := d.ListFamilyGroupDevices(groupID)
	if err != nil {
		return nil, fmt.Errorf("获取家庭组设备失败: %v", err)
	}

	// 关联用户信息
	for i := range members {
		user, err := d.GetUser(members[i].UserID)
		if err == nil {
			members[i].UserInfo = user
		}
	}

	// 关联设备信息
	for i := range devices {
		device, err := d.GetDevice(devices[i].DeviceID)
		if err == nil {
			devices[i].DeviceInfo = device
		}
	}

	return &FamilyGroupWithDetails{
		GroupInfo: *group,
		Members:   members,
		Devices:   devices,
	}, nil
}

// CheckUserInFamilyGroup 检查用户是否在家庭组中
func (d *Database) CheckUserInFamilyGroup(userID, groupID string) (bool, int8, error) {
	var member FamilyGroupMember
	err := d.db.Where("group_id = ? AND user_id = ?", groupID, userID).First(&member).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return false, 0, nil
		}
		return false, 0, err
	}
	return true, member.Role, nil
}

// CreateFamilyGroupInvitation 创建家庭组邀请
func (d *Database) CreateFamilyGroupInvitation(invitation *FamilyGroupInvitation) error {
	// 检查是否已存在未处理的邀请
	var count int64
	if err := d.db.Model(&FamilyGroupInvitation{}).
		Where("group_id = ? AND invitee_id = ? AND status = 0", invitation.GroupID, invitation.InviteeID).
		Count(&count).Error; err != nil {
		return fmt.Errorf("检查邀请是否存在时出错: %v", err)
	}

	if count > 0 {
		return fmt.Errorf("已存在对该用户的待处理邀请")
	}

	// 检查被邀请者是否已经是组成员
	if err := d.db.Model(&FamilyGroupMember{}).
		Where("group_id = ? AND user_id = ?", invitation.GroupID, invitation.InviteeID).
		Count(&count).Error; err != nil {
		return fmt.Errorf("检查用户是否已是组成员时出错: %v", err)
	}

	if count > 0 {
		return fmt.Errorf("该用户已经是家庭组成员")
	}

	// 创建邀请
	return d.db.Create(invitation).Error
}

// GetFamilyGroupInvitation 获取家庭组邀请
func (d *Database) GetFamilyGroupInvitation(invitationID string) (*FamilyGroupInvitation, error) {
	var invitation FamilyGroupInvitation
	if err := d.db.Where("invitation_id = ?", invitationID).First(&invitation).Error; err != nil {
		return nil, err
	}
	return &invitation, nil
}

// ListSentFamilyGroupInvitations 获取用户发送的家庭组邀请
func (d *Database) ListSentFamilyGroupInvitations(userID string) ([]FamilyGroupInvitation, error) {
	var invitations []FamilyGroupInvitation
	if err := d.db.Where("inviter_id = ?", userID).Find(&invitations).Error; err != nil {
		return nil, err
	}
	return invitations, nil
}

// ListReceivedFamilyGroupInvitations 获取用户收到的家庭组邀请
func (d *Database) ListReceivedFamilyGroupInvitations(userID string) ([]FamilyGroupInvitation, error) {
	var invitations []FamilyGroupInvitation
	if err := d.db.Where("invitee_id = ? AND status = 0", userID).Find(&invitations).Error; err != nil {
		return nil, err
	}
	return invitations, nil
}

// UpdateFamilyGroupInvitation 更新家庭组邀请状态
func (d *Database) UpdateFamilyGroupInvitation(invitationID string, status int8) error {
	return d.db.Model(&FamilyGroupInvitation{}).
		Where("invitation_id = ?", invitationID).
		Update("status", status).Error
}

// DeleteFamilyGroupInvitation 删除家庭组邀请
func (d *Database) DeleteFamilyGroupInvitation(invitationID string) error {
	return d.db.Where("invitation_id = ?", invitationID).Delete(&FamilyGroupInvitation{}).Error
}

// ProcessExpiredInvitations 处理过期邀请
func (d *Database) ProcessExpiredInvitations() error {
	return d.db.Model(&FamilyGroupInvitation{}).
		Where("expire_at IS NOT NULL AND expire_at < ? AND status = 0", time.Now()).
		Update("status", 3).Error
}

// GetUserByEmail 通过邮箱获取用户
func (d *Database) GetUserByEmail(email string) (*User, error) {
	var user User
	if err := d.db.Where("email = ?", email).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("未找到邮箱为 %s 的用户", email)
		}
		return nil, fmt.Errorf("查询用户时发生错误: %v", err)
	}
	return &user, nil
}

// ==================== 设备OTA状态相关方法 ====================

// GetDeviceOTAStatus 获取设备OTA状态
func (d *Database) GetDeviceOTAStatus(deviceID string) (*DeviceOTAStatus, error) {
	var status DeviceOTAStatus
	if err := d.db.Where("device_id = ?", deviceID).First(&status).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			// 如果记录不存在，创建一个默认状态
			status = DeviceOTAStatus{
				DeviceID:    deviceID,
				Status:      "idle",
				LastUpdated: time.Now(),
			}
			if createErr := d.db.Create(&status).Error; createErr != nil {
				return nil, fmt.Errorf("创建默认OTA状态失败: %v", createErr)
			}
		} else {
			return nil, err
		}
	}
	return &status, nil
}

// UpdateDeviceOTAStatus 更新设备OTA状态
func (d *Database) UpdateDeviceOTAStatus(deviceID, status string) error {
	// 先尝试更新，如果记录不存在则创建
	result := d.db.Model(&DeviceOTAStatus{}).
		Where("device_id = ?", deviceID).
		Update("status", status)

	if result.Error != nil {
		return result.Error
	}

	// 如果没有记录被更新，说明记录不存在，需要创建
	if result.RowsAffected == 0 {
		otaStatus := &DeviceOTAStatus{
			DeviceID:    deviceID,
			Status:      status,
			LastUpdated: time.Now(),
		}
		return d.db.Create(otaStatus).Error
	}

	return nil
}

// ResetExpiredOTAStatus 重置超时的OTA状态
// 如果3分钟没有更新，自动设置为idle状态
func (d *Database) ResetExpiredOTAStatus() error {
	timeout := time.Now().Add(-3 * time.Minute)
	return d.db.Model(&DeviceOTAStatus{}).
		Where("last_updated < ? AND status != ?", timeout, "idle").
		Update("status", "idle").Error
}

// ListAllOTAStatuses 获取所有设备的OTA状态（用于监控）
func (d *Database) ListAllOTAStatuses() ([]DeviceOTAStatus, error) {
	var statuses []DeviceOTAStatus
	if err := d.db.Find(&statuses).Error; err != nil {
		return nil, err
	}
	return statuses, nil
}

// GetOTADownloadCache 获取OTA下载链接缓存
func (d *Database) GetOTADownloadCache(version string) (*OTADownloadCache, error) {
	var cache OTADownloadCache
	err := d.db.Where("version = ?", version).First(&cache).Error
	if err != nil {
		return nil, err
	}
	return &cache, nil
}

// CreateOrUpdateOTADownloadCache 创建或更新OTA下载链接缓存
func (d *Database) CreateOrUpdateOTADownloadCache(cache *OTADownloadCache) error {
	// 先尝试更新
	result := d.db.Where("version = ?", cache.Version).Updates(cache)
	if result.Error != nil {
		return result.Error
	}

	// 如果没有找到记录，则创建新的
	if result.RowsAffected == 0 {
		return d.db.Create(cache).Error
	}

	return nil
}

// DeleteSoonToExpireOTADownloadCache 删除剩余时间小于2天的OTA下载链接缓存
func (d *Database) DeleteSoonToExpireOTADownloadCache() error {
	// 计算2天后的时间，清理在2天内过期的缓存
	twoDays := time.Now().Add(time.Hour * 48)
	return d.db.Where("expires_at < ?", twoDays).Delete(&OTADownloadCache{}).Error
}

// ==================== 设备传感器状态相关方法 ====================

// GetDeviceSensorStatus 获取设备传感器状态
func (d *Database) GetDeviceSensorStatus(deviceID string) (*DeviceSensorStatus, error) {
	var status DeviceSensorStatus
	if err := d.db.Where("device_id = ?", deviceID).First(&status).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			// 如果记录不存在，创建一个新的空状态记录
			status = DeviceSensorStatus{
				DeviceID:  deviceID,
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
			}
			if createErr := d.db.Create(&status).Error; createErr != nil {
				return nil, fmt.Errorf("创建设备传感器状态记录失败: %v", createErr)
			}
		} else {
			return nil, err
		}
	}
	return &status, nil
}

// UpdateDeviceSensorError 更新设备特定传感器的错误时间和错误类型
func (d *Database) UpdateDeviceSensorError(deviceID, sensorType string, errorTime time.Time, errorType string) error {
	// 首先确保设备存在传感器状态记录
	_, err := d.GetDeviceSensorStatus(deviceID)
	if err != nil {
		return fmt.Errorf("获取设备传感器状态失败: %v", err)
	}

	// 根据传感器类型更新对应字段
	var updates map[string]interface{}
	switch sensorType {
	case "camera":
		updates = map[string]interface{}{
			"camera_last_error_time": errorTime,
			"camera_last_error_type": errorType,
		}
	case "weight_sensor":
		updates = map[string]interface{}{
			"weight_sensor_last_error_time": errorTime,
			"weight_sensor_last_error_type": errorType,
		}
	case "temperature_humidity_sensor":
		updates = map[string]interface{}{
			"temperature_humidity_sensor_last_error_time": errorTime,
			"temperature_humidity_sensor_last_error_type": errorType,
		}
	case "microphone":
		updates = map[string]interface{}{
			"microphone_last_error_time": errorTime,
			"microphone_last_error_type": errorType,
		}
	case "wifi":
		updates = map[string]interface{}{
			"wifi_last_error_time": errorTime,
			"wifi_last_error_type": errorType,
		}
	case "bluetooth":
		updates = map[string]interface{}{
			"bluetooth_last_error_time": errorTime,
			"bluetooth_last_error_type": errorType,
		}
	default:
		return fmt.Errorf("不支持的传感器类型: %s", sensorType)
	}

	// 更新记录
	return d.db.Model(&DeviceSensorStatus{}).
		Where("device_id = ?", deviceID).
		Updates(updates).Error
}

// ListDevicesWithSensorErrors 获取有传感器错误的设备列表
func (d *Database) ListDevicesWithSensorErrors() ([]DeviceSensorStatus, error) {
	var statuses []DeviceSensorStatus
	if err := d.db.Where(
		"camera_last_error_time IS NOT NULL OR " +
			"weight_sensor_last_error_time IS NOT NULL OR " +
			"temperature_humidity_sensor_last_error_time IS NOT NULL OR " +
			"microphone_last_error_time IS NOT NULL OR " +
			"wifi_last_error_time IS NOT NULL OR " +
			"bluetooth_last_error_time IS NOT NULL",
	).Find(&statuses).Error; err != nil {
		return nil, err
	}
	return statuses, nil
}

// ClearDeviceSensorError 清除设备特定传感器的错误时间和错误类型
func (d *Database) ClearDeviceSensorError(deviceID, sensorType string) error {
	// 根据传感器类型清除对应字段
	var updates map[string]interface{}
	switch sensorType {
	case "camera":
		updates = map[string]interface{}{
			"camera_last_error_time": nil,
			"camera_last_error_type": nil,
		}
	case "weight_sensor":
		updates = map[string]interface{}{
			"weight_sensor_last_error_time": nil,
			"weight_sensor_last_error_type": nil,
		}
	case "temperature_humidity_sensor":
		updates = map[string]interface{}{
			"temperature_humidity_sensor_last_error_time": nil,
			"temperature_humidity_sensor_last_error_type": nil,
		}
	case "microphone":
		updates = map[string]interface{}{
			"microphone_last_error_time": nil,
			"microphone_last_error_type": nil,
		}
	case "wifi":
		updates = map[string]interface{}{
			"wifi_last_error_time": nil,
			"wifi_last_error_type": nil,
		}
	case "bluetooth":
		updates = map[string]interface{}{
			"bluetooth_last_error_time": nil,
			"bluetooth_last_error_type": nil,
		}
	default:
		return fmt.Errorf("不支持的传感器类型: %s", sensorType)
	}

	// 更新记录
	return d.db.Model(&DeviceSensorStatus{}).
		Where("device_id = ?", deviceID).
		Updates(updates).Error
}

// ==================== 用户影子模式配置相关方法 ====================

// GetUserShadowConfig 获取用户影子模式配置
func (d *Database) GetUserShadowConfig(userID string) (*UserShadowConfig, error) {
	var config UserShadowConfig
	err := d.db.Where("user_id = ?", userID).First(&config).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 返回默认配置
			return &UserShadowConfig{
				UserID:              userID,
				Enabled:             false,
				SimilarityThreshold: 0.85,
				NewCatThreshold:     0.70,
				TopK:                5,
				NotificationEnabled: false,
			}, nil
		}
		return nil, err
	}
	return &config, nil
}

// SaveUserShadowConfig 保存用户影子模式配置
func (d *Database) SaveUserShadowConfig(config *UserShadowConfig) error {
	// 使用GORM的Save方法，如果记录存在则更新，不存在则创建
	return d.db.Save(config).Error
}

// DisableUserShadowMode 禁用用户影子模式
func (d *Database) DisableUserShadowMode(userID string) error {
	return d.db.Model(&UserShadowConfig{}).
		Where("user_id = ?", userID).
		Update("enabled", false).Error
}

// UpdateUserShadowConfig 更新用户影子模式配置
func (d *Database) UpdateUserShadowConfig(config *UserShadowConfig) error {
	return d.db.Model(&UserShadowConfig{}).
		Where("user_id = ?", config.UserID).
		Updates(map[string]interface{}{
			"enabled":              config.Enabled,
			"similarity_threshold": config.SimilarityThreshold,
			"new_cat_threshold":    config.NewCatThreshold,
			"top_k":                config.TopK,
			"notification_enabled": config.NotificationEnabled,
			"updated_at":           time.Now(),
		}).Error
}
