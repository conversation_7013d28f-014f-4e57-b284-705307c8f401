package cattoilet

import "time"

// ==================== 用户相关表 ====================

// User 用户基本信息
type User struct {
	UserID       string     `gorm:"primaryKey;column:user_id" json:"user_id"`    // UUID
	LogtoID      string     `gorm:"uniqueIndex;column:logto_id" json:"logto_id"` // Logto 用户 ID
	Username     string     `gorm:"uniqueIndex;not null" json:"username"`
	PasswordHash string     `gorm:"column:password_hash;not null" json:"-"`
	Email        string     `gorm:"index" json:"email"`
	Phone        string     `gorm:"index" json:"phone"`
	Nickname     string     `json:"nickname"`
	Status       int8       `gorm:"default:1" json:"status"` // 1-正常 2-禁用
	CreatedAt    time.Time  `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt    time.Time  `gorm:"autoUpdateTime" json:"updated_at"`
	LastLogin    *time.Time `json:"last_login,omitempty"`

	// 一对多关系
	Cats    []Cat    `gorm:"foreignKey:UserID" json:"cats,omitempty"`    // 用户拥有的猫咪
	Devices []Device `gorm:"foreignKey:UserID" json:"devices,omitempty"` // 用户拥有的设备
}

// UserProfile 用户详细信息
type UserProfile struct {
	UserID       string     `gorm:"primaryKey;column:user_id" json:"user_id"`
	Nickname     string     `json:"nickname"`
	RealName     string     `gorm:"type:varchar(64)" json:"real_name"`
	Gender       int8       `gorm:"default:0" json:"gender"`
	Birthday     *time.Time `gorm:"type:date" json:"birthday"`
	AvatarURL    string     `gorm:"column:avatar_url" json:"avatar_url"`
	Address      string     `gorm:"type:varchar(255)" json:"address"`
	City         string     `gorm:"type:varchar(64)" json:"city"`
	Country      string     `gorm:"type:varchar(64)" json:"country"`
	EmergContact string     `gorm:"type:varchar(64)" json:"emerg_contact"`
	EmergPhone   string     `gorm:"type:varchar(20)" json:"emerg_phone"`
	IsVerified   bool       `gorm:"default:false" json:"is_verified"`
	UpdatedAt    time.Time  `gorm:"autoUpdateTime" json:"updated_at"`
	User         *User      `gorm:"foreignKey:UserID" json:"-"`
}

// UserSettings 用户设置
type UserSettings struct {
	UserID       string    `gorm:"primaryKey;column:user_id" json:"user_id"`
	Language     string    `gorm:"type:varchar(8);default:'zh'" json:"language"`
	TimeZone     string    `gorm:"type:varchar(32)" json:"time_zone"`
	Notification bool      `gorm:"default:true" json:"notification"`
	Newsletter   bool      `gorm:"default:true" json:"newsletter"`
	Theme        string    `gorm:"type:varchar(16)" json:"theme"`
	Settings     string    `gorm:"type:text" json:"settings"` // 其他设置(JSON)
	UpdatedAt    time.Time `gorm:"autoUpdateTime" json:"updated_at"`
	User         *User     `gorm:"foreignKey:UserID" json:"-"`
}

// UserLog 用户日志
type UserLog struct {
	ID          int64     `gorm:"primaryKey;autoIncrement" json:"id"`
	UserID      string    `gorm:"index" json:"user_id"`
	Type        string    `gorm:"type:varchar(32)" json:"type"` // login/logout/operation
	IP          string    `gorm:"type:varchar(39)" json:"ip"`
	UserAgent   string    `gorm:"type:varchar(255)" json:"user_agent"`
	Description string    `gorm:"type:text" json:"description"`
	Data        string    `gorm:"type:text" json:"data"` // 详细数据(JSON)
	CreatedAt   time.Time `gorm:"autoCreateTime" json:"created_at"`
	User        *User     `gorm:"foreignKey:UserID" json:"-"`
}

// UserRelation 用户关系(好友/家庭成员)
type UserRelation struct {
	ID          int64     `gorm:"primaryKey;autoIncrement" json:"id"`
	UserID      string    `gorm:"index:idx_users" json:"user_id"`    // 用户ID
	RelatedID   string    `gorm:"index:idx_users" json:"related_id"` // 关联用户ID
	Type        int8      `json:"type"`                              // 1-好友 2-家庭成员
	Role        int8      `json:"role"`                              // 家庭成员角色：1-主人 2-家人 3-临时照看
	Permissions string    `gorm:"type:text" json:"permissions"`      // 权限设置(JSON)
	Status      int8      `gorm:"default:1" json:"status"`           // 1-正常 2-已解除
	CreatedAt   time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt   time.Time `gorm:"autoUpdateTime" json:"updated_at"`
	User        *User     `gorm:"foreignKey:UserID" json:"-"`
	Related     *User     `gorm:"foreignKey:RelatedID" json:"related,omitempty"`
	SharerID    string    `json:"sharer_id"` // 分享者ID
}

// ==================== 设备相关表 ====================

// Device 设备基本信息
type Device struct {
	DeviceID        string     `gorm:"primaryKey;column:device_id" json:"device_id"` // 业务编码：CAT_20240222_0001
	UserID          string     `gorm:"column:user_id;not null;index" json:"user_id"`
	HardwareSN      string     `gorm:"uniqueIndex;not null" json:"hardware_sn"`
	Name            string     `json:"name"`
	Model           string     `gorm:"not null" json:"model"`
	Timezone        string     `gorm:"type:varchar(32)" json:"timezone"`
	FirmwareVersion string     `json:"firmware_version"`
	Status          int8       `gorm:"default:1" json:"status"`
	LastHeartbeat   *time.Time `json:"last_heartbeat"`
	LastActive      *time.Time `json:"last_active"`
	CreatedAt       time.Time  `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt       time.Time  `gorm:"autoUpdateTime" json:"updated_at"`
	User            *User      `gorm:"foreignKey:UserID" json:"-"`

	// 关联
	Videos []RecordShit `gorm:"foreignKey:DeviceID" json:"videos,omitempty"` // 设备的视频记录
}

// DeviceStatusRequest 设备状态请求
type DeviceStatusRequest struct {
	DeviceID        string     `json:"device_id" binding:"required"`
	SignalStrength  int8       `json:"signal_strength"`
	IPv4            string     `json:"ipv4"`
	IPv6            string     `json:"ipv6"`
	LastBootTime    *time.Time `json:"last_boot_time"`
	LastConnectTime *time.Time `json:"last_connect_time"`
	StorageUsage    int8       `json:"storage_usage"`
}

// DeviceStatus(update frequently)
type DeviceStatus struct {
	DeviceID  string    `gorm:"primaryKey;column:device_id" json:"device_id"`
	Online    bool      `json:"online"`
	IPv4      string    `json:"ipv4"`
	IPv6      string    `json:"ipv6"`
	UpdatedAt time.Time `gorm:"autoUpdateTime" json:"updated_at"`
}

// DeviceAllSettings 设备所有设置
type DeviceAllSettings struct {
	DeviceID            string    `gorm:"primaryKey;column:device_id" json:"device_id"`
	AutoOTAUpgrade      string    `gorm:"column:auto_ota_upgrade" json:"auto_ota_upgrade"`                       // "on" or "off"
	IdleUpdateStartHour int8      `gorm:"column:idle_update_start_hour;default:2" json:"idle_update_start_hour"` // 闲时更新开始时间（小时，0-23）
	IdleUpdateEndHour   int8      `gorm:"column:idle_update_end_hour;default:4" json:"idle_update_end_hour"`     // 闲时更新结束时间（小时，0-23）
	UpdatedAt           time.Time `gorm:"autoUpdateTime" json:"updated_at"`
}

// DeviceStatusStatistics
type DeviceStatusStatistics struct {
	DeviceID          string    `gorm:"primaryKey;column:device_id" json:"device_id"`
	OnlineRate        int8      `json:"online_rate"`
	PowerSupplyRate   int8      `json:"power_supply_rate"`
	SignalStrengthAvg int8      `json:"signal_strength_average"`
	StorageUsage      int8      `json:"storage_usage"`
	UpdatedAt         time.Time `gorm:"autoUpdateTime" json:"updated_at"`
}

// DeviceConfig 设备配置
type DeviceConfig struct {
	DeviceID        string    `gorm:"primaryKey;column:device_id" json:"device_id"`
	Location        string    `gorm:"type:varchar(64)" json:"location"`
	RoomSize        float64   `gorm:"type:decimal(5,2)" json:"room_size"`
	InstallHeight   float64   `gorm:"type:decimal(4,2)" json:"install_height"`
	InstallAngle    float64   `gorm:"type:decimal(5,2)" json:"install_angle"`
	GPS             *string   `gorm:"type:point" json:"gps"`
	CameraConfig    string    `gorm:"type:text" json:"camera_config"`
	WeightConfig    string    `gorm:"type:text" json:"weight_config"`
	AccelConfig     string    `gorm:"type:text" json:"accel_config"`
	EnvSensorConfig string    `gorm:"type:text" json:"env_sensor_config"`
	AIConfig        string    `gorm:"type:text" json:"ai_config"`
	AIVersion       string    `json:"ai_version"`
	Settings        string    `gorm:"type:text" json:"settings"`
	UpdatedAt       time.Time `gorm:"autoUpdateTime" json:"updated_at"`
	Device          *Device   `gorm:"foreignKey:DeviceID" json:"-"`
}

// DeviceMaintenance 设备维护记录
type DeviceMaintenance struct {
	ID              int64      `gorm:"primaryKey;autoIncrement" json:"id"`
	DeviceID        string     `gorm:"index" json:"device_id"`
	Type            string     `gorm:"type:varchar(32)" json:"type"`     // 维护类型
	Operator        string     `gorm:"type:varchar(64)" json:"operator"` // 维护人员
	Description     string     `gorm:"type:text" json:"description"`
	MaintenanceTime time.Time  `json:"maintenance_time"`
	NextTime        *time.Time `json:"next_time"`
	CreatedAt       time.Time  `gorm:"autoCreateTime" json:"created_at"`
	Device          *Device    `gorm:"foreignKey:DeviceID" json:"-"`
}

// DeviceLog 设备日志
type DeviceLog struct {
	ID        int64     `gorm:"primaryKey;autoIncrement" json:"id"`
	DeviceID  string    `gorm:"index" json:"device_id"`
	Type      string    `gorm:"type:varchar(32)" json:"type"` // error/warning/info
	Level     int8      `json:"level"`                        // 日志级别
	Message   string    `gorm:"type:text" json:"message"`
	Data      string    `gorm:"type:text" json:"data"` // 详细数据(JSON)
	CreatedAt time.Time `gorm:"autoCreateTime" json:"created_at"`
	Device    *Device   `gorm:"foreignKey:DeviceID" json:"-"`
}

// DeviceShare 设备分享
type DeviceShare struct {
	ID          int64      `gorm:"primaryKey;autoIncrement" json:"id"`
	DeviceID    string     `gorm:"index:idx_device_user" json:"device_id"`
	UserID      string     `gorm:"index:idx_device_user" json:"user_id"` // 被分享用户
	SharerID    string     `json:"sharer_id"`                            // 分享者ID
	Permissions string     `gorm:"type:text" json:"permissions"`         // 权限设置(JSON)
	ExpireAt    *time.Time `json:"expire_at"`                            // 分享过期时间
	Status      int8       `gorm:"default:1" json:"status"`              // 1-有效 2-已过期 3-已撤销
	CreatedAt   time.Time  `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt   time.Time  `gorm:"autoUpdateTime" json:"updated_at"`
	Device      *Device    `gorm:"foreignKey:DeviceID" json:"-"`
	User        *User      `gorm:"foreignKey:UserID" json:"user,omitempty"`
	Sharer      *User      `gorm:"foreignKey:SharerID" json:"sharer,omitempty"`
}

// ==================== 猫咪相关表 ====================

// Cat 猫咪基本信息
type Cat struct {
	CatID     string     `gorm:"primaryKey;column:cat_id" json:"cat_id"` // UUID
	UserID    string     `gorm:"column:user_id;not null;index" json:"user_id"`
	Name      string     `gorm:"not null" json:"name"`
	Birthday  *time.Time `gorm:"type:date" json:"birthday"`
	Gender    int8       `gorm:"default:0" json:"gender"`
	Breed     string     `gorm:"type:varchar(32)" json:"breed"`
	Color     string     `gorm:"type:varchar(32)" json:"color"`
	Status    int8       `gorm:"default:1;index" json:"status"`
	CreatedAt time.Time  `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt time.Time  `gorm:"autoUpdateTime" json:"updated_at"`
	User      *User      `gorm:"foreignKey:UserID" json:"-"`
}

// CatBehavior 猫咪行为特征
type CatBehavior struct {
	CatID           string    `gorm:"primaryKey;column:cat_id" json:"cat_id"`
	ToiletFrequency int       `json:"toilet_frequency"`
	DietPreference  string    `gorm:"type:text" json:"diet_preference"`
	ActivityLevel   int8      `gorm:"default:2" json:"activity_level"`
	IndoorOnly      bool      `gorm:"default:true" json:"indoor_only"`
	FacialFeatures  string    `gorm:"type:text" json:"facial_features"`
	BodyFeatures    string    `gorm:"type:text" json:"body_features"`
	BehaviorTags    string    `gorm:"type:text" json:"behavior_tags"`
	UpdatedAt       time.Time `gorm:"autoUpdateTime" json:"updated_at"`
	Cat             *Cat      `gorm:"foreignKey:CatID" json:"-"`
}

// CatRelation 猫咪关系
type CatRelation struct {
	ID          int64     `gorm:"primaryKey;autoIncrement" json:"id"`
	CatID       string    `gorm:"index:idx_cats" json:"cat_id"`
	RelatedID   string    `gorm:"index:idx_cats" json:"related_id"`
	Type        int8      `json:"type"` // 1-父母 2-子女 3-兄弟姐妹
	Description string    `gorm:"type:text" json:"description"`
	CreatedAt   time.Time `gorm:"autoCreateTime" json:"created_at"`
	Cat         *Cat      `gorm:"foreignKey:CatID" json:"-"`
	Related     *Cat      `gorm:"foreignKey:RelatedID" json:"related,omitempty"`
}

// ==================== 行为记录相关表 ====================

// RecordShit 排泄记录

type ApiRecordShit struct {
	UserId string     `json:"user_id"`
	Record RecordShit `json:"record"`
}

type RecordShit struct {
	VideoID      string    `json:"video_id" gorm:"primaryKey"`
	DeviceID     string    `json:"device_id"`
	StartTime    int64     `json:"start_time" gorm:"column:start_time"` // Unix timestamp in seconds
	EndTime      *int64    `json:"end_time" gorm:"column:end_time"`     // Unix timestamp in seconds
	Status       int8      `json:"status" gorm:"default:1"`             // 1=正常,2=删除
	ProcessStage int8      `json:"process_stage" gorm:"default:0"`      // 0=未处理,1=处理中,2=已完成
	WeightLitter float64   `json:"weight_litter"`
	WeightCat    float64   `json:"weight_cat"`
	WeightWaste  float64   `json:"weight_waste"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
	// 关联
	Device   *Device         `json:"device,omitempty" gorm:"foreignKey:DeviceID"`
	Analysis *RecordAnalysis `json:"analysis,omitempty" gorm:"foreignKey:VideoID"`
}

// RecordAnalysis 视频分析结果
type RecordAnalysis struct {
	VideoID       string  `json:"video_id" gorm:"primaryKey"`
	AnimalID      string  `json:"animal_id"`
	CatConfidence float64 `json:"cat_confidence"`
	BehaviorType  string  `json:"behavior_type"`
	IsAbnormal    bool    `json:"is_abnormal" gorm:"default:false"`
	AbnormalType  string  `json:"abnormal_type"`
	AbnormalProb  float64 `json:"abnormal_prob"`
	// 影子模式相关字段（只保留重要信息）
	ShadowSimilarity   *float64  `json:"shadow_similarity,omitempty" gorm:"type:decimal(5,4)"`
	ShadowMatchedCatID *string   `json:"shadow_matched_cat_id,omitempty" gorm:"type:varchar(32)"`
	ShadowModelVersion *string   `json:"shadow_model_version,omitempty" gorm:"type:varchar(32)"`
	CreatedAt          time.Time `json:"created_at"`
	UpdatedAt          time.Time `json:"updated_at"`
	// 关联
	Video *RecordShit `json:"-" gorm:"foreignKey:VideoID"`
}

// ShadowModeNotification 影子模式通知记录 (简化版)
type ShadowModeNotification struct {
	ID               int64     `json:"id" gorm:"primaryKey;autoIncrement"`
	UserID           string    `json:"user_id" gorm:"column:user_id;not null;index"`
	VideoID          string    `json:"video_id" gorm:"column:video_id;not null"`
	NotificationType string    `json:"notification_type" gorm:"type:enum('new_cat','different_result','low_confidence');not null"`
	MessageTitle     string    `json:"message_title" gorm:"type:varchar(255);not null"`
	MessageContent   *string   `json:"message_content,omitempty" gorm:"type:text"`
	CreatedAt        time.Time `json:"created_at" gorm:"autoCreateTime"`
	User             *User     `json:"-" gorm:"foreignKey:UserID"`
}

// ==================== 统计相关表 ====================

// CatMetricsDaily 猫咪每日统计
type CatMetricsDaily struct {
	ID         int64     `gorm:"primaryKey;autoIncrement" json:"id"`
	CatID      string    `gorm:"column:cat_id;not null;index:idx_cat_date" json:"cat_id"`
	MetricDate time.Time `gorm:"column:metric_date;type:date;not null;index:idx_cat_date" json:"metric_date"`

	// 体重数据
	Weight         float64 `gorm:"type:decimal(5,2)" json:"weight"`
	WeightMin      float64 `gorm:"type:decimal(5,2)" json:"weight_min"`
	WeightMax      float64 `gorm:"type:decimal(5,2)" json:"weight_max"`
	WeightVariance float64 `gorm:"type:decimal(5,3)" json:"weight_variance"`

	// 如厕统计
	ToiletCount   int `gorm:"default:0" json:"toilet_count"`
	UrineCount    int `gorm:"default:0" json:"urine_count"`
	StoolCount    int `gorm:"default:0" json:"stool_count"`
	AbnormalCount int `gorm:"default:0" json:"abnormal_count"`

	// 时间统计
	TotalDuration int `gorm:"default:0" json:"total_duration"`
	AvgDuration   int `gorm:"default:0" json:"avg_duration"`

	// 排泄物统计
	TotalWasteWeight float64 `gorm:"type:decimal(6,3)" json:"total_waste_weight"`
	AvgWasteWeight   float64 `gorm:"type:decimal(5,3)" json:"avg_waste_weight"`

	// 时间分布
	MorningCount   int `json:"morning_count"`
	AfternoonCount int `json:"afternoon_count"`
	EveningCount   int `json:"evening_count"`
	NightCount     int `json:"night_count"`

	// 健康指标
	HealthScore    float64 `gorm:"type:decimal(4,1)" json:"health_score"`
	HydrationLevel float64 `gorm:"type:decimal(3,2)" json:"hydration_level"`
	DigestionScore float64 `gorm:"type:decimal(3,2)" json:"digestion_score"`

	CreatedAt time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt time.Time `gorm:"autoUpdateTime" json:"updated_at"`
	Cat       *Cat      `gorm:"foreignKey:CatID" json:"-"`
}

// CatMetricsMonthly 猫咪月度统计（聚合数据）
type CatMetricsMonthly struct {
	ID    int64  `gorm:"primaryKey;autoIncrement" json:"id"`
	CatID string `gorm:"column:cat_id;not null;index:idx_cat_month" json:"cat_id"`
	Year  int    `gorm:"not null;index:idx_cat_month" json:"year"`
	Month int    `gorm:"not null;index:idx_cat_month" json:"month"`

	// 月度汇总
	AvgWeight        float64 `gorm:"type:decimal(5,2)" json:"avg_weight"`
	WeightTrend      float64 `gorm:"type:decimal(4,2)" json:"weight_trend"`
	TotalToiletCount int     `json:"total_toilet_count"`
	AvgToiletCount   float64 `gorm:"type:decimal(4,1)" json:"avg_toilet_count"`
	TotalAbnormal    int     `json:"total_abnormal"`

	// 健康评估
	HealthScore float64 `gorm:"type:decimal(4,1)" json:"health_score"`
	HealthTrend float64 `gorm:"type:decimal(4,1)" json:"health_trend"`
	Suggestions string  `gorm:"type:text" json:"suggestions"`

	CreatedAt time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt time.Time `gorm:"autoUpdateTime" json:"updated_at"`
	Cat       *Cat      `gorm:"foreignKey:CatID" json:"-"`
}

// CatAlert 猫咪异常警报
type CatAlert struct {
	ID          int64      `gorm:"primaryKey;autoIncrement" json:"id"`
	CatID       string     `gorm:"index" json:"cat_id"`
	Type        string     `gorm:"type:varchar(32)" json:"type"`
	Level       int8       `gorm:"default:1" json:"level"`
	Title       string     `gorm:"type:varchar(128)" json:"title"`
	Description string     `gorm:"type:text" json:"description"`
	Suggestions string     `gorm:"type:text" json:"suggestions"`
	Status      int8       `gorm:"default:1" json:"status"`
	ProcessTime *time.Time `json:"process_time"`
	ProcessNote string     `gorm:"type:text" json:"process_note"`
	CreatedAt   time.Time  `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt   time.Time  `gorm:"autoUpdateTime" json:"updated_at"`
	Cat         *Cat       `gorm:"foreignKey:CatID" json:"-"`
}

// Notification 通知消息
type Notification struct {
	ID        int64      `gorm:"primaryKey;autoIncrement" json:"id"`
	UserID    string     `gorm:"index" json:"user_id"`
	Type      string     `gorm:"type:varchar(32)" json:"type"`
	Title     string     `gorm:"type:varchar(128)" json:"title"`
	Content   string     `gorm:"type:text" json:"content"`
	Level     int8       `gorm:"default:1" json:"level"`
	IsRead    bool       `gorm:"default:false" json:"is_read"`
	ReadTime  *time.Time `json:"read_time"`
	ExtraData string     `gorm:"type:text" json:"extra_data"`
	CreatedAt time.Time  `gorm:"autoCreateTime" json:"created_at"`
	User      *User      `gorm:"foreignKey:UserID" json:"-"`
}

// UserHardware 用户硬件关联
type UserHardware struct {
	ID         int64     `gorm:"primaryKey;autoIncrement" json:"id"`
	UserID     string    `gorm:"index:idx_user_hw;not null" json:"user_id"` // 用户ID
	HardwareSN string    `gorm:"uniqueIndex;not null" json:"hardware_sn"`   // 硬件序列号
	Status     int8      `gorm:"default:1" json:"status"`                   // 状态：1-正常 2-禁用
	Remark     string    `gorm:"type:varchar(255)" json:"remark"`           // 备注
	CreatedAt  time.Time `gorm:"autoCreateTime" json:"created_at"`          // 创建时间
	UpdatedAt  time.Time `gorm:"autoUpdateTime" json:"updated_at"`          // 更新时间

	// 关联
	User *User `gorm:"foreignKey:UserID" json:"user,omitempty"` // 关联用户
}

// UserHardwareRequest 添加用户硬件关联请求
type UserHardwareRequest struct {
	UserID     string `json:"user_id" example:"abc123def456"`        // 用户ID
	HardwareSN string `json:"hardware_sn" example:"HW2024022600001"` // 硬件序列号
	Remark     string `json:"remark,omitempty" example:"客厅设备"`       // 备注
}

// HardwareUserResponse 硬件用户响应
type HardwareUserResponse struct {
	UserID     string `json:"user_id"`     // 用户ID
	HardwareSN string `json:"hardware_sn"` // 硬件序列号
}

// CatDailyMetrics 猫咪每日指标
type CatDailyMetrics struct {
	CatID       string    `json:"cat_id"`
	Date        time.Time `json:"date"`
	VisitCount  int       `json:"visit_count"`
	AvgDuration float64   `json:"avg_duration"`
	Weight      float64   `json:"weight"`
	// ... 其他需要的字段
}

// CatMonthlyMetrics 猫咪月度指标
type CatMonthlyMetrics struct {
	CatID       string  `json:"cat_id"`
	Year        int     `json:"year"`
	Month       int     `json:"month"`
	VisitCount  int     `json:"visit_count"`
	AvgDuration float64 `json:"avg_duration"`
	Weight      float64 `json:"weight"`
	// ... 其他需要的字段
}

// TableName 指定表名
func (User) TableName() string                 { return "users" }
func (Device) TableName() string               { return "devices" }
func (Cat) TableName() string                  { return "cats" }
func (RecordShit) TableName() string           { return "record_shit" }
func (UserProfile) TableName() string          { return "user_profiles" }
func (UserSettings) TableName() string         { return "user_settings" }
func (UserLog) TableName() string              { return "user_logs" }
func (DeviceStatus) TableName() string         { return "device_status" }
func (DeviceConfig) TableName() string         { return "device_configs" }
func (DeviceMaintenance) TableName() string    { return "device_maintenance" }
func (DeviceLog) TableName() string            { return "device_logs" }
func (CatBehavior) TableName() string          { return "cat_behaviors" }
func (RecordAnalysis) TableName() string       { return "record_analysis" }
func (CatMetricsDaily) TableName() string      { return "cat_metrics_daily" }
func (CatMetricsMonthly) TableName() string    { return "cat_metrics_monthly" }
func (CatAlert) TableName() string             { return "cat_alerts" }
func (UserRelation) TableName() string         { return "user_relations" }
func (DeviceShare) TableName() string          { return "device_shares" }
func (CatRelation) TableName() string          { return "cat_relations" }
func (Notification) TableName() string         { return "notifications" }
func (UserHardware) TableName() string         { return "user_hardware" }
func (Client) TableName() string               { return "clients" }
func (ClientToken) TableName() string          { return "clients_tokens" }
func (NotificationSettings) TableName() string { return "notification_settings" }

// DeviceToken 设备令牌信息
type DeviceToken struct {
	ID          int64     `json:"id"`
	DeviceID    string    `json:"device_id"`
	UserID      string    `json:"user_id"`
	DeviceToken string    `json:"device_token"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// DeviceTokenRequest 设备令牌注册请求
type DeviceTokenRequest struct {
	DeviceToken string `form:"deviceToken" binding:"required"`
	DeviceID    string `form:"deviceId" binding:"required"`
	UserID      string `form:"userID" binding:"required"`
}

// Client 客户端信息
type Client struct {
	ID         int64     `gorm:"primaryKey;autoIncrement" json:"id"`
	UserID     string    `gorm:"column:user_id;not null" json:"user_id"`
	ClientID   string    `gorm:"column:client_id;not null" json:"client_id"`
	ClientType string    `gorm:"column:client_type;not null" json:"client_type"` // ios/android/web
	Name       string    `gorm:"column:name" json:"name"`                        // 设备名称
	Model      string    `gorm:"column:model" json:"model"`                      // 设备型号
	OSVersion  string    `gorm:"column:os_version" json:"os_version"`            // 操作系统版本
	AppVersion string    `gorm:"column:app_version" json:"app_version"`          // APP版本
	LastActive time.Time `gorm:"column:last_active" json:"last_active"`          // 最后活跃时间
	Status     int8      `gorm:"column:status;default:1" json:"status"`          // 1-正常 2-禁用
	CreatedAt  time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt  time.Time `gorm:"autoUpdateTime" json:"updated_at"`
	User       *User     `gorm:"foreignKey:UserID" json:"-"`
}

// ClientToken 客户端令牌信息
type ClientToken struct {
	ID          int64     `gorm:"primaryKey;autoIncrement" json:"id"`
	ClientID    string    `gorm:"column:client_id;not null" json:"client_id"`
	UserID      string    `gorm:"column:user_id;not null" json:"user_id"`
	ClientToken string    `gorm:"column:client_token;not null" json:"client_token"`
	TokenType   string    `gorm:"column:token_type;default:apns" json:"token_type"` // apns/fcm/web
	IsSandbox   bool      `gorm:"column:is_sandbox;default:false" json:"is_sandbox"`
	Status      int8      `gorm:"column:status;default:1" json:"status"`
	CreatedAt   time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt   time.Time `gorm:"autoUpdateTime" json:"updated_at"`
	Client      *Client   `gorm:"foreignKey:ClientID" json:"-"`
	User        *User     `gorm:"foreignKey:UserID" json:"-"`
}

// ClientRegisterRequest 客户端注册请求
type ClientRegisterRequest struct {
	UserID     string `json:"user_id" binding:"required"`
	ClientID   string `json:"client_id" binding:"required"`   // Base64 编码的设备ID
	ClientType string `json:"client_type" binding:"required"` // ios/android/web
	Name       string `json:"name"`                           // 设备名称
	Model      string `json:"model"`                          // 设备型号
	OSVersion  string `json:"os_version"`                     // 操作系统版本
	AppVersion string `json:"app_version"`                    // APP版本
}

// ClientTokenRequest 客户端令牌注册请求
type ClientTokenRequest struct {
	ClientToken string `json:"client_token" binding:"required"`
	TokenType   string `json:"token_type" default:"apns"`
	IsSandbox   bool   `json:"is_sandbox" default:"false"`
}

// ClientHeartbeatRequest 客户端心跳请求
type ClientHeartbeatRequest struct {
	AppVersion string `json:"app_version"`
	Status     int8   `json:"status"`
}

// ClientResponse 客户端信息响应
type ClientResponse struct {
	ID         int64     `json:"id"`
	UserID     string    `json:"user_id"`
	ClientID   string    `json:"client_id"`
	ClientType string    `json:"client_type"`
	Name       string    `json:"name"`
	Model      string    `json:"model"`
	OSVersion  string    `json:"os_version"`
	AppVersion string    `json:"app_version"`
	LastActive time.Time `json:"last_active"`
	Status     int8      `json:"status"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
}

// ClientUpdateRequest 客户端更新请求
type ClientUpdateRequest struct {
	Name       string `json:"name"`
	Model      string `json:"model"`
	OSVersion  string `json:"os_version"`
	AppVersion string `json:"app_version"`
	Status     *int8  `json:"status"`
}

// ClientStatusRequest 客户端状态更新请求
type ClientStatusRequest struct {
	Status     int8   `json:"status" binding:"required"`
	AppVersion string `json:"app_version"`
}

// ClientTokenResponse 客户端令牌响应
type ClientTokenResponse struct {
	ID          int64     `json:"id"`
	ClientID    string    `json:"client_id"`
	ClientToken string    `json:"client_token"`
	TokenType   string    `json:"token_type"`
	IsSandbox   bool      `json:"is_sandbox"`
	Status      int8      `json:"status"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// ErrorResponse 错误响应
type ErrorResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Error   string `json:"error,omitempty"`
}

// BaseResponse 基础响应
type BaseResponse struct {
	Code    int         `json:"code"`
	Status  string      `json:"status"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

// NotificationSettings 通知设置
type NotificationSettings struct {
	UserID          string    `gorm:"column:user_id;primaryKey" json:"user_id"`
	EnableDaily     bool      `gorm:"column:enable_daily;default:true" json:"enable_daily"`
	EnableStats     bool      `gorm:"column:enable_stats;default:true" json:"enable_stats"`
	QuietHoursStart int       `gorm:"column:quiet_hours_start;default:22" json:"quiet_hours_start"`
	QuietHoursEnd   int       `gorm:"column:quiet_hours_end;default:7" json:"quiet_hours_end"`
	Timezone        string    `gorm:"column:timezone;default:'Asia/Shanghai'" json:"timezone"`
	CreatedAt       time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt       time.Time `gorm:"autoUpdateTime" json:"updated_at"`
}

// 家庭组相关结构体

// FamilyGroup 家庭组
type FamilyGroup struct {
	GroupID     string    `gorm:"primaryKey;column:group_id" json:"group_id"`
	GroupName   string    `gorm:"column:group_name" json:"group_name" binding:"required"`
	OwnerID     string    `gorm:"column:owner_id" json:"owner_id"`
	Description string    `gorm:"column:description" json:"description"`
	MaxMembers  int       `gorm:"column:max_members;default:8" json:"max_members"`
	CreatedAt   time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt   time.Time `gorm:"autoUpdateTime" json:"updated_at"`
}

// FamilyGroupMemberRole 家庭组成员角色
const (
	FamilyMemberRoleNormal int8 = iota // 0 - 普通成员
	FamilyMemberRoleAdmin              // 1 - 管理员
	FamilyMemberRoleOwner              // 2 - 拥有者
)

// FamilyGroupMember 家庭组成员
type FamilyGroupMember struct {
	ID        int64     `gorm:"primaryKey;autoIncrement" json:"id"`
	GroupID   string    `gorm:"column:group_id" json:"group_id"`
	UserID    string    `gorm:"column:user_id" json:"user_id"`
	Nickname  string    `gorm:"column:nickname" json:"nickname"`
	Role      int8      `gorm:"column:role" json:"role"`
	JoinTime  time.Time `gorm:"column:join_time;autoCreateTime" json:"join_time"`
	UpdatedAt time.Time `gorm:"autoUpdateTime" json:"updated_at"`
	// 非数据库字段，用于返回用户详细信息
	UserInfo *User `gorm:"-" json:"user_info,omitempty"`
}

// FamilyGroupDevice 家庭组设备关联
type FamilyGroupDevice struct {
	ID        int64     `gorm:"primaryKey;autoIncrement" json:"id"`
	GroupID   string    `gorm:"column:group_id" json:"group_id"`
	DeviceID  string    `gorm:"column:device_id" json:"device_id"`
	AddedBy   string    `gorm:"column:added_by" json:"added_by"`
	CreatedAt time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt time.Time `gorm:"autoUpdateTime" json:"updated_at"`
	// 非数据库字段，用于返回设备详细信息
	DeviceInfo *Device `gorm:"-" json:"device_info,omitempty"`
}

// FamilyGroupCreateRequest 创建家庭组请求
type FamilyGroupCreateRequest struct {
	GroupName   string `json:"group_name" binding:"required"`
	Description string `json:"description"`
}

// FamilyGroupUpdateRequest 更新家庭组请求
type FamilyGroupUpdateRequest struct {
	GroupName   string `json:"group_name"`
	Description string `json:"description"`
}

// FamilyGroupMemberAddRequest 添加家庭组成员请求
type FamilyGroupMemberAddRequest struct {
	UserID   string `json:"user_id" binding:"required"`
	Nickname string `json:"nickname"`
	Role     int8   `json:"role"`
}

// FamilyGroupMemberUpdateRequest 更新家庭组成员请求
type FamilyGroupMemberUpdateRequest struct {
	Nickname string `json:"nickname"`
	Role     int8   `json:"role"`
}

// FamilyGroupDeviceAddRequest 添加家庭组设备请求
type FamilyGroupDeviceAddRequest struct {
	DeviceID string `json:"device_id" binding:"required"`
}

// FamilyGroupResponse 家庭组响应
type FamilyGroupResponse struct {
	GroupID     string              `json:"group_id"`
	GroupName   string              `json:"group_name"`
	OwnerID     string              `json:"owner_id"`
	Description string              `json:"description"`
	MaxMembers  int                 `json:"max_members"`
	MemberCount int                 `json:"member_count"`
	DeviceCount int                 `json:"device_count"`
	CreatedAt   time.Time           `json:"created_at"`
	UpdatedAt   time.Time           `json:"updated_at"`
	Members     []FamilyGroupMember `json:"members,omitempty"`
	Devices     []FamilyGroupDevice `json:"devices,omitempty"`
}

// FamilyGroupWithDetails 带详情的家庭组
type FamilyGroupWithDetails struct {
	GroupInfo FamilyGroup         `json:"group_info"`
	Members   []FamilyGroupMember `json:"members"`
	Devices   []FamilyGroupDevice `json:"devices"`
}

// FamilyGroupInvitation 家庭组邀请
type FamilyGroupInvitation struct {
	InvitationID string     `gorm:"primaryKey;column:invitation_id" json:"invitation_id"`
	GroupID      string     `gorm:"column:group_id" json:"group_id"`
	InviterID    string     `gorm:"column:inviter_id" json:"inviter_id"`
	InviteeID    string     `gorm:"column:invitee_id" json:"invitee_id"`
	Status       int8       `gorm:"column:status;default:0" json:"status"` // 0-待处理, 1-已接受, 2-已拒绝, 3-已过期
	Role         int8       `gorm:"column:role;default:0" json:"role"`     // 0-普通成员, 1-管理员
	CreatedAt    time.Time  `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt    time.Time  `gorm:"autoUpdateTime" json:"updated_at"`
	ExpireAt     *time.Time `gorm:"column:expire_at" json:"expire_at,omitempty"`

	// 非数据库字段，用于返回邀请者和被邀请者详细信息
	InviterInfo *User        `gorm:"-" json:"inviter_info,omitempty"`
	InviteeInfo *User        `gorm:"-" json:"invitee_info,omitempty"`
	GroupInfo   *FamilyGroup `gorm:"-" json:"group_info,omitempty"`
}

// FamilyGroupInvitationCreateRequest 创建家庭组邀请请求
type FamilyGroupInvitationCreateRequest struct {
	InviteeID string `json:"invitee_id" binding:"required"`
	Role      int8   `json:"role"`
	ExpireAt  string `json:"expire_at,omitempty"` // ISO8601格式的过期时间，可选
}

// FamilyGroupInvitationCreateByEmailRequest 通过邮箱创建家庭组邀请请求
type FamilyGroupInvitationCreateByEmailRequest struct {
	InviteeEmail string `json:"invitee_email" binding:"required"` // 被邀请者邮箱
	Role         int8   `json:"role"`                             // 角色
	ExpireAt     string `json:"expire_at,omitempty"`              // ISO8601格式的过期时间，可选
}

// FamilyGroupInvitationResponse 家庭组邀请响应
type FamilyGroupInvitationResponse struct {
	InvitationID string     `json:"invitation_id"`
	GroupID      string     `json:"group_id"`
	GroupName    string     `json:"group_name"`
	InviterID    string     `json:"inviter_id"`
	InviterName  string     `json:"inviter_name"`
	InviteeID    string     `json:"invitee_id"`
	InviteeName  string     `json:"invitee_name"` // Added field for invitee's name
	Status       int8       `json:"status"`
	Role         int8       `json:"role"`
	CreatedAt    time.Time  `json:"created_at"`
	ExpireAt     *time.Time `json:"expire_at,omitempty"`
}

// TableName 指定表名
func (FamilyGroupInvitation) TableName() string { return "family_group_invitations" }

// DeviceOTAStatus 设备OTA状态
type DeviceOTAStatus struct {
	DeviceID    string    `gorm:"primaryKey;column:device_id" json:"device_id"`
	Status      string    `gorm:"column:status;default:'idle'" json:"status"` // 'idle', 'updating', 'failed', 'completed'
	LastUpdated time.Time `gorm:"autoUpdateTime" json:"last_updated"`
	Device      *Device   `gorm:"foreignKey:DeviceID" json:"-"`
}

func (DeviceOTAStatus) TableName() string { return "device_ota_status" }

// DeviceOTAStatusRequest 设备OTA状态请求
type DeviceOTAStatusRequest struct {
	DeviceID string `json:"device_id" binding:"required"`
	Status   string `json:"status" binding:"required"` // 'idle', 'updating', 'failed', 'completed'
}

// DeviceOTAStatusResponse 设备OTA状态响应
type DeviceOTAStatusResponse struct {
	DeviceID    string    `json:"device_id"`
	Status      string    `json:"status"`
	LastUpdated time.Time `json:"last_updated"`
}

// OTADownloadCache OTA下载链接缓存
type OTADownloadCache struct {
	ID          int       `gorm:"primaryKey;autoIncrement" json:"id"`
	Version     string    `gorm:"column:version;uniqueIndex;not null" json:"version"` // OTA版本号
	DownloadURL string    `gorm:"column:download_url;type:text" json:"download_url"`  // 下载链接
	MD5URL      string    `gorm:"column:md5_url;type:text" json:"md5_url"`            // MD5下载链接
	GeneratedAt time.Time `gorm:"column:generated_at;not null" json:"generated_at"`   // 生成时间
	ExpiresAt   time.Time `gorm:"column:expires_at;not null" json:"expires_at"`       // 过期时间
	CreatedAt   time.Time `gorm:"column:created_at;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt   time.Time `gorm:"column:updated_at;default:CURRENT_TIMESTAMP" json:"updated_at"`
}

func (OTADownloadCache) TableName() string {
	return "ota_download_cache"
}

// DeviceSensorStatus 设备传感器状态
type DeviceSensorStatus struct {
	DeviceID                               string     `gorm:"primaryKey;column:device_id" json:"device_id"`
	CameraLastErrorTime                    *time.Time `gorm:"column:camera_last_error_time" json:"camera_last_error_time"`
	CameraLastErrorType                    *string    `gorm:"column:camera_last_error_type" json:"camera_last_error_type"`
	WeightSensorLastErrorTime              *time.Time `gorm:"column:weight_sensor_last_error_time" json:"weight_sensor_last_error_time"`
	WeightSensorLastErrorType              *string    `gorm:"column:weight_sensor_last_error_type" json:"weight_sensor_last_error_type"`
	TemperatureHumiditySensorLastErrorTime *time.Time `gorm:"column:temperature_humidity_sensor_last_error_time" json:"temperature_humidity_sensor_last_error_time"`
	TemperatureHumiditySensorLastErrorType *string    `gorm:"column:temperature_humidity_sensor_last_error_type" json:"temperature_humidity_sensor_last_error_type"`
	MicrophoneLastErrorTime                *time.Time `gorm:"column:microphone_last_error_time" json:"microphone_last_error_time"`
	MicrophoneLastErrorType                *string    `gorm:"column:microphone_last_error_type" json:"microphone_last_error_type"`
	WifiLastErrorTime                      *time.Time `gorm:"column:wifi_last_error_time" json:"wifi_last_error_time"`
	WifiLastErrorType                      *string    `gorm:"column:wifi_last_error_type" json:"wifi_last_error_type"`
	BluetoothLastErrorTime                 *time.Time `gorm:"column:bluetooth_last_error_time" json:"bluetooth_last_error_time"`
	BluetoothLastErrorType                 *string    `gorm:"column:bluetooth_last_error_type" json:"bluetooth_last_error_type"`
	CreatedAt                              time.Time  `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt                              time.Time  `gorm:"autoUpdateTime" json:"updated_at"`
	Device                                 *Device    `gorm:"foreignKey:DeviceID" json:"-"`
}

func (DeviceSensorStatus) TableName() string {
	return "device_sensor_status"
}

// DeviceSensorErrorRequest 设备传感器错误上报请求
type DeviceSensorErrorRequest struct {
	DeviceID       string `json:"device_id" binding:"required"`
	SensorType     string `json:"sensor_type" binding:"required"` // camera, weight_sensor, temperature_humidity_sensor, microphone, wifi, bluetooth
	ErrorCode      int    `json:"error_code,omitempty"`
	ErrorMessage   string `json:"error_message,omitempty"`
	AdditionalInfo string `json:"additional_info,omitempty"`
	ReportTime     int64  `json:"report_time,omitempty"` // Unix timestamp, 如果不提供则使用当前时间
}

// DeviceSensorStatusResponse 设备传感器状态响应
type DeviceSensorStatusResponse struct {
	DeviceID                               string     `json:"device_id"`
	CameraLastErrorTime                    *time.Time `json:"camera_last_error_time"`
	CameraLastErrorType                    *string    `json:"camera_last_error_type"`
	WeightSensorLastErrorTime              *time.Time `json:"weight_sensor_last_error_time"`
	WeightSensorLastErrorType              *string    `json:"weight_sensor_last_error_type"`
	TemperatureHumiditySensorLastErrorTime *time.Time `json:"temperature_humidity_sensor_last_error_time"`
	TemperatureHumiditySensorLastErrorType *string    `json:"temperature_humidity_sensor_last_error_type"`
	MicrophoneLastErrorTime                *time.Time `json:"microphone_last_error_time"`
	MicrophoneLastErrorType                *string    `json:"microphone_last_error_type"`
	WifiLastErrorTime                      *time.Time `json:"wifi_last_error_time"`
	WifiLastErrorType                      *string    `json:"wifi_last_error_type"`
	BluetoothLastErrorTime                 *time.Time `json:"bluetooth_last_error_time"`
	BluetoothLastErrorType                 *string    `json:"bluetooth_last_error_type"`
	UpdatedAt                              time.Time  `json:"updated_at"`
}

// ==================== 用户影子模式配置 ====================

// UserShadowConfig 用户影子模式配置
type UserShadowConfig struct {
	UserID              string    `json:"user_id" gorm:"primaryKey;type:varchar(20)"`
	Enabled             bool      `json:"enabled" gorm:"default:false"`
	SimilarityThreshold float64   `json:"similarity_threshold" gorm:"type:decimal(5,4);default:0.8500"`
	NewCatThreshold     float64   `json:"new_cat_threshold" gorm:"type:decimal(5,4);default:0.7000"`
	TopK                int       `json:"top_k" gorm:"default:5"`
	NotificationEnabled bool      `json:"notification_enabled" gorm:"default:false"`
	CreatedAt           time.Time `json:"created_at"`
	UpdatedAt           time.Time `json:"updated_at"`
}

// TableName 指定表名
func (UserShadowConfig) TableName() string {
	return "user_shadow_config"
}
