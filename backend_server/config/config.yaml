minio:
  endpoint: "***************:9000"
  access_key: "animsuper"
  secret_key: "4vbtCeEQgcN2uB"
  use_ssl: false

minio_ota:
  endpoint: "***************:9000"
  access_key: "animsuper"
  secret_key: "4vbtCeEQgcN2uB"
  use_ssl: false

mysql:
  host: "***************"
  port: 3306
  user: "meowth"
  password: "wzjJZfG3Qzj4j9"
  database: "cats_db"

aps:
  team_id: "5WZYK34FW5"
  key_id: "5MXK6AA45M"
  bundle_id: "com.animsilicon.CabyCare"
  # auth_key_path: "/Users/<USER>/AuthKey_5MXK6AA45M.p8"
  # auth_key_path: "/home/<USER>/AuthKey_5MXK6AA45M.p8"
  # auth_key_path: "/home/<USER>/Downloads/AuthKey_5MXK6AA45M.p8"
  # auth_key_path: "/Users/<USER>/AuthKey_5MXK6AA45M.p8"
  auth_key_path: "/root/.cert/notification/AuthKey_5MXK6AA45M.p8"

logto:
  endpoint: "https://login.caby.care"
  app_id: "d2oz2qj0ppjdpf6de14ha"
  app_secret: "wR1zHgk6e0EZrxoNrvHmxHHjIHWWFskG"
  api_resource: "https://brx8db.logto.app/api"
  callback_uri: "api.cabycare://callback"

caby_backend:
  url: "https://api.caby.care"

caby_ai:
  url: "http://***************:8765"
  auth_token: "03U66tGbSQtHGrh9IyBDjRYaSeQukFga"
  max_retries: 300
  timeout_minutes: 30

# Shadow mode configuration
shadow_mode:
  enabled: true
  similarity_threshold: 0.85
  new_cat_threshold: 0.70
  top_k: 5
  notification_enabled: true

# Device monitor configuration
device_config:
  heartbeat_interval: 15  # Device heartbeat interval in minutes (default 15)
  status_timeout: 31      # Time after which device is considered offline (default 30 minutes)
  statistics_window: 24   # Window for statistics calculation (default 24 hours)
