version: "3"

services:
  backend-server:
    build:
      context: .
      dockerfile: Dockerfile.backend
      network: host # 构建时使用主机网络
    ports:
      - "5678:5678"
    volumes:
      - ./config:/app/config
      - /root/.cert:/root/.cert
    environment:
      - CONFIG_PATH=/app/config/config.yaml
    depends_on:
      mysql:
        condition: service_healthy
      minio:
        condition: service_healthy
    env_file:
      - .env

  minio:
    build:
      context: .
      dockerfile: Dockerfile.minio
      network: host
      args:
        MINIO_VERSION: 20250228095516.0.0
        http_proxy: "${http_proxy}" # 构建时使用代理
        https_proxy: "${https_proxy}"
        no_proxy: "${no_proxy}"
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - ./data/minio:/data/minio
    environment:
      - MINIO_ROOT_USER=${MINIO_ROOT_USER:-}
      - MINIO_ROOT_PASSWORD=${MINIO_ROOT_PASSWORD:-}
    env_file:
      - .env
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 20s

  mysql:
    build:
      context: .
      dockerfile: Dockerfile.mysql
    ports:
      - "3306:3306"
    volumes:
      - ./data/mysql:/var/lib/mysql
      - ./scripts/init-db.sh:/docker-entrypoint-initdb.d/init-db.sh
    environment:
      - MYSQL_DATABASE=${MYSQL_DATABASE:-cats_db}
      - MYSQL_USER=${MYSQL_USER:-meowth}
      - MYSQL_PASSWORD=${MYSQL_PASSWORD:-}
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD:-}
      - SETUP_MODE=${SETUP_MODE:-auto}
      - INIT_DB=${INIT_DB:-true}
    env_file:
      - .env
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u$$MYSQL_USER", "-p$$MYSQL_PASSWORD"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

volumes:
  mysql_data:
  minio_data:

networks:
  caby_network:
    external: true
