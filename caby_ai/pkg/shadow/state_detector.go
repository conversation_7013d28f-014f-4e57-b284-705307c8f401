package shadow

import (
	"context"
	"log"
	"math"

	"caby-ai/pkg/qdrant"
)

// StateDetector 状态检测器
type StateDetector struct {
	qdrantClient *qdrant.QdrantClient
}

// NewStateDetector 创建状态检测器
func NewStateDetector(qdrantClient *qdrant.QdrantClient) *StateDetector {
	return &StateDetector{
		qdrantClient: qdrantClient,
	}
}

// AppearanceState 外观状态
type AppearanceState struct {
	State           string  `json:"state"`            // "normal", "abnormal", "uncertain"
	Confidence      float64 `json:"confidence"`       // 状态置信度
	AbnormalityType string  `json:"abnormality_type"` // "medical", "clothing", "grooming", "unknown"
	Duration        int     `json:"duration_days"`    // 状态持续天数
	IsTemporary     bool    `json:"is_temporary"`     // 是否为临时状态
	ShouldProtect   bool    `json:"should_protect"`   // 是否应该保护现有特征
}

// DetectAppearanceState 检测外观状态
func (sd *StateDetector) DetectAppearanceState(ctx context.Context, userID, catID string, currentFeatures []float64) (*AppearanceState, error) {
	// 1. 获取该猫咪的历史特征
	historicalFeatures, err := sd.getRecentFeatures(ctx, userID, catID, 30) // 最近30天
	if err != nil {
		log.Printf("Warning: failed to get historical features: %v", err)
		return sd.getDefaultState(), nil
	}

	if len(historicalFeatures) < 5 {
		// 历史数据不足，返回默认状态
		return sd.getDefaultState(), nil
	}

	// 2. 计算特征分布变化
	distributionChange := sd.calculateDistributionChange(currentFeatures, historicalFeatures)

	// 3. 检测异常模式
	abnormalityScore := sd.detectAbnormalityPatterns(currentFeatures, historicalFeatures)

	// 4. 分析时间模式
	temporalPattern := sd.analyzeTemporalPattern(historicalFeatures)

	// 5. 综合判断状态
	state := sd.classifyState(distributionChange, abnormalityScore, temporalPattern)

	log.Printf("State detection for cat %s: %s (confidence: %.3f)",
		catID, state.State, state.Confidence)

	return state, nil
}

// calculateDistributionChange 计算特征分布变化
func (sd *StateDetector) calculateDistributionChange(current []float64, historical [][]float64) float64 {
	if len(historical) == 0 {
		return 0.0
	}

	// 计算历史特征的平均值
	historicalMean := sd.calculateHistoricalMean(historical)

	// 计算当前特征与历史平均的差异
	totalDiff := 0.0
	for i, val := range current {
		if i < len(historicalMean) {
			diff := math.Abs(val - historicalMean[i])
			totalDiff += diff
		}
	}

	// 归一化差异值
	avgDiff := totalDiff / float64(len(current))

	return avgDiff
}

// detectAbnormalityPatterns 检测异常模式
func (sd *StateDetector) detectAbnormalityPatterns(current []float64, historical [][]float64) float64 {
	if len(historical) < 3 {
		return 0.0
	}

	// 计算当前特征与最近几个特征的相似度
	recentCount := int(math.Min(5, float64(len(historical))))
	recent := historical[len(historical)-recentCount:]

	similarities := make([]float64, len(recent))
	for i, hist := range recent {
		similarities[i] = sd.calculateCosineSimilarity(current, hist)
	}

	// 计算平均相似度
	avgSimilarity := 0.0
	for _, sim := range similarities {
		avgSimilarity += sim
	}
	avgSimilarity /= float64(len(similarities))

	// 异常分数 = 1 - 平均相似度
	abnormalityScore := 1.0 - avgSimilarity

	return abnormalityScore
}

// analyzeTemporalPattern 分析时间模式
func (sd *StateDetector) analyzeTemporalPattern(historical [][]float64) map[string]interface{} {
	pattern := map[string]interface{}{
		"stability":     0.5,
		"trend":         "stable",
		"duration_days": 0,
	}

	if len(historical) < 7 {
		return pattern
	}

	// 计算最近一周的特征稳定性
	recent := historical[len(historical)-7:]
	stability := sd.calculateStability(recent)
	pattern["stability"] = stability

	// 简单的趋势分析
	if stability < 0.3 {
		pattern["trend"] = "changing"
	} else if stability > 0.7 {
		pattern["trend"] = "stable"
	} else {
		pattern["trend"] = "moderate"
	}

	pattern["duration_days"] = len(historical)

	return pattern
}

// classifyState 分类状态
func (sd *StateDetector) classifyState(distributionChange, abnormalityScore float64, temporalPattern map[string]interface{}) *AppearanceState {
	state := &AppearanceState{
		State:         "normal",
		Confidence:    0.5,
		IsTemporary:   false,
		ShouldProtect: false,
	}

	// 基于分布变化和异常分数判断
	changeScore := (distributionChange + abnormalityScore) / 2.0

	if changeScore > 0.6 {
		// 高变化分数，可能是异常状态
		state.State = "abnormal"
		state.Confidence = changeScore
		state.IsTemporary = true
		state.ShouldProtect = true // 保护现有特征，避免被异常状态污染

		// 尝试分类异常类型
		if distributionChange > 0.7 {
			state.AbnormalityType = "medical" // 可能是医疗相关
		} else if abnormalityScore > 0.7 {
			state.AbnormalityType = "clothing" // 可能是穿戴相关
		} else {
			state.AbnormalityType = "unknown"
		}

	} else if changeScore > 0.3 {
		// 中等变化，不确定状态
		state.State = "uncertain"
		state.Confidence = 0.5
		state.IsTemporary = false
		state.ShouldProtect = false

	} else {
		// 低变化分数，正常状态
		state.State = "normal"
		state.Confidence = 1.0 - changeScore
		state.IsTemporary = false
		state.ShouldProtect = false
	}

	// 考虑时间模式
	if stability, ok := temporalPattern["stability"].(float64); ok {
		if stability < 0.3 && state.State == "abnormal" {
			// 不稳定且异常，更可能是临时状态
			state.IsTemporary = true
			state.ShouldProtect = true
		}
	}

	if duration, ok := temporalPattern["duration_days"].(int); ok {
		state.Duration = duration

		// 如果异常状态持续超过30天，可能不是临时的
		if state.State == "abnormal" && duration > 30 {
			state.IsTemporary = false
			state.ShouldProtect = false
		}
	}

	return state
}

// 辅助方法
func (sd *StateDetector) getDefaultState() *AppearanceState {
	return &AppearanceState{
		State:           "normal",
		Confidence:      0.5,
		AbnormalityType: "unknown",
		Duration:        0,
		IsTemporary:     false,
		ShouldProtect:   false,
	}
}

func (sd *StateDetector) getRecentFeatures(ctx context.Context, userID, catID string, days int) ([][]float64, error) {
	// TODO: 实现从Qdrant获取最近N天的特征
	// 这需要在Qdrant中按时间戳查询特征
	// 暂时返回空数组
	return [][]float64{}, nil
}

func (sd *StateDetector) calculateHistoricalMean(historical [][]float64) []float64 {
	if len(historical) == 0 {
		return []float64{}
	}

	featureSize := len(historical[0])
	mean := make([]float64, featureSize)

	for _, features := range historical {
		for i, val := range features {
			if i < featureSize {
				mean[i] += val
			}
		}
	}

	for i := range mean {
		mean[i] /= float64(len(historical))
	}

	return mean
}

func (sd *StateDetector) calculateCosineSimilarity(a, b []float64) float64 {
	if len(a) != len(b) {
		return 0.0
	}

	dotProduct := 0.0
	normA := 0.0
	normB := 0.0

	for i := range a {
		dotProduct += a[i] * b[i]
		normA += a[i] * a[i]
		normB += b[i] * b[i]
	}

	if normA == 0 || normB == 0 {
		return 0.0
	}

	return dotProduct / (math.Sqrt(normA) * math.Sqrt(normB))
}

func (sd *StateDetector) calculateStability(features [][]float64) float64 {
	if len(features) < 2 {
		return 1.0
	}

	totalSimilarity := 0.0
	count := 0

	for i := 0; i < len(features)-1; i++ {
		similarity := sd.calculateCosineSimilarity(features[i], features[i+1])
		totalSimilarity += similarity
		count++
	}

	return totalSimilarity / float64(count)
}
