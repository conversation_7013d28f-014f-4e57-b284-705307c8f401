package shadow

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

// StateClient 状态检查客户端
type StateClient struct {
	baseURL    string
	httpClient *http.Client
	authToken  string
}

// NewStateClient 创建状态检查客户端
func NewStateClient(baseURL, authToken string) *StateClient {
	return &StateClient{
		baseURL:    baseURL,
		authToken:  authToken,
		httpClient: &http.Client{Timeout: 10 * time.Second},
	}
}

// CatStateResponse 猫咪状态响应
type CatStateResponse struct {
	Success bool `json:"success"`
	Data    struct {
		CatID            string    `json:"cat_id"`
		HasState         bool      `json:"has_state"`
		State            string    `json:"state,omitempty"`
		Description      string    `json:"description,omitempty"`
		StartTime        time.Time `json:"start_time,omitempty"`
		ExpectedEndTime  time.Time `json:"expected_end_time,omitempty"`
		ExpectedDuration int       `json:"expected_duration,omitempty"`
		IsActive         bool      `json:"is_active,omitempty"`
	} `json:"data"`
}

// CheckCatState 检查猫咪状态
func (sc *StateClient) CheckCatState(ctx context.Context, userID, catID string) (*CatStateResponse, error) {
	url := fmt.Sprintf("%s/api/cats/%s/state", sc.baseURL, catID)
	
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}
	
	// 设置认证头
	if sc.authToken != "" {
		req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", sc.authToken))
	}
	
	// 设置用户ID头（如果backend_server需要）
	req.Header.Set("X-User-ID", userID)
	
	resp, err := sc.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("request failed with status %d: %s", resp.StatusCode, string(body))
	}
	
	var stateResp CatStateResponse
	if err := json.NewDecoder(resp.Body).Decode(&stateResp); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}
	
	return &stateResp, nil
}

// MarkCatStateRequest 标记猫咪状态请求
type MarkCatStateRequest struct {
	State            string `json:"state"`
	Description      string `json:"description"`
	ExpectedDuration int    `json:"expected_duration_days"`
}

// MarkCatState 标记猫咪状态
func (sc *StateClient) MarkCatState(ctx context.Context, userID, catID, state, description string, expectedDuration int) error {
	url := fmt.Sprintf("%s/api/cats/%s/state", sc.baseURL, catID)
	
	reqBody := MarkCatStateRequest{
		State:            state,
		Description:      description,
		ExpectedDuration: expectedDuration,
	}
	
	jsonData, err := json.Marshal(reqBody)
	if err != nil {
		return fmt.Errorf("failed to marshal request: %w", err)
	}
	
	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}
	
	req.Header.Set("Content-Type", "application/json")
	if sc.authToken != "" {
		req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", sc.authToken))
	}
	req.Header.Set("X-User-ID", userID)
	
	resp, err := sc.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("request failed with status %d: %s", resp.StatusCode, string(body))
	}
	
	return nil
}

// EndCatState 结束猫咪状态
func (sc *StateClient) EndCatState(ctx context.Context, userID, catID string) error {
	url := fmt.Sprintf("%s/api/cats/%s/state", sc.baseURL, catID)
	
	req, err := http.NewRequestWithContext(ctx, "DELETE", url, nil)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}
	
	if sc.authToken != "" {
		req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", sc.authToken))
	}
	req.Header.Set("X-User-ID", userID)
	
	resp, err := sc.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("request failed with status %d: %s", resp.StatusCode, string(body))
	}
	
	return nil
}
