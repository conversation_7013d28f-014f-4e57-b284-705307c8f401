package shadow

import (
	"context"
	"log"
	"time"
)

// FeatureProtector 特征保护器
type FeatureProtector struct {
	config *ShadowModeConfig
}

// NewFeatureProtector 创建特征保护器
func NewFeatureProtector(config *ShadowModeConfig) *FeatureProtector {
	return &FeatureProtector{
		config: config,
	}
}

// FeatureTier 特征层级
type FeatureTier string

const (
	TierCore      FeatureTier = "core"      // 核心特征：永久保护
	TierBaseline  FeatureTier = "baseline"  // 基线特征：长期保护
	TierAdaptive  FeatureTier = "adaptive"  // 适应特征：可演化
	TierTemporary FeatureTier = "temporary" // 临时特征：短期保护后删除
)

// ProtectedFeature 受保护的特征
type ProtectedFeature struct {
	ID              string      `json:"id"`
	CatID           string      `json:"cat_id"`
	UserID          string      `json:"user_id"`
	Tier            FeatureTier `json:"tier"`
	QualityScore    float64     `json:"quality_score"`
	CreatedAt       time.Time   `json:"created_at"`
	LastMatchedAt   time.Time   `json:"last_matched_at"`
	MatchCount      int         `json:"match_count"`
	ProtectionLevel int         `json:"protection_level"` // 1-5，越高越难删除
	IsBaseline      bool        `json:"is_baseline"`      // 是否为基线特征
}

// ClassifyFeatureTier 分类特征层级
func (fp *FeatureProtector) ClassifyFeatureTier(
	qualityScore float64,
	matchCount int,
	age time.Duration,
	isBaseline bool,
	currentState *AppearanceState,
) FeatureTier {
	
	// 1. 核心特征：高质量 + 高匹配频次 + 基线特征
	if qualityScore >= 0.85 && matchCount >= 10 && isBaseline {
		return TierCore
	}
	
	// 2. 基线特征：正常状态下的高质量特征
	if qualityScore >= 0.75 && isBaseline && currentState.State == "normal" {
		return TierBaseline
	}
	
	// 3. 临时特征：异常状态下的特征
	if currentState.State == "abnormal" && currentState.IsTemporary {
		return TierTemporary
	}
	
	// 4. 适应特征：其他情况
	return TierAdaptive
}

// CalculateProtectionLevel 计算保护级别
func (fp *FeatureProtector) CalculateProtectionLevel(feature *ProtectedFeature) int {
	level := 1 // 基础保护级别
	
	// 基于层级调整
	switch feature.Tier {
	case TierCore:
		level = 5 // 最高保护
	case TierBaseline:
		level = 4 // 高保护
	case TierAdaptive:
		level = 2 // 中等保护
	case TierTemporary:
		level = 1 // 低保护
	}
	
	// 基于质量调整
	if feature.QualityScore >= 0.9 {
		level += 1
	} else if feature.QualityScore < 0.5 {
		level -= 1
	}
	
	// 基于匹配频次调整
	if feature.MatchCount >= 20 {
		level += 1
	} else if feature.MatchCount < 3 {
		level -= 1
	}
	
	// 基于年龄调整
	age := time.Since(feature.CreatedAt)
	if age < 7*24*time.Hour { // 新特征
		level -= 1
	} else if age > 90*24*time.Hour { // 老特征
		level += 1
	}
	
	// 确保在合理范围内
	if level < 1 {
		level = 1
	}
	if level > 5 {
		level = 5
	}
	
	return level
}

// ShouldProtectFeature 判断是否应该保护特征
func (fp *FeatureProtector) ShouldProtectFeature(
	feature *ProtectedFeature,
	currentState *AppearanceState,
	evolutionPressure int, // 1-5，演化压力等级
) bool {
	
	protectionLevel := fp.CalculateProtectionLevel(feature)
	
	// 核心特征永远保护
	if feature.Tier == TierCore {
		return true
	}
	
	// 在异常状态下，保护基线特征
	if currentState.ShouldProtect && feature.Tier == TierBaseline {
		return true
	}
	
	// 基于保护级别和演化压力判断
	return protectionLevel > evolutionPressure
}

// GetEvolutionCandidates 获取可演化的特征候选
func (fp *FeatureProtector) GetEvolutionCandidates(
	allFeatures []*ProtectedFeature,
	currentState *AppearanceState,
	targetCount int,
) []*ProtectedFeature {
	
	candidates := make([]*ProtectedFeature, 0)
	
	// 1. 过滤出不受保护的特征
	for _, feature := range allFeatures {
		evolutionPressure := fp.calculateEvolutionPressure(len(allFeatures), targetCount)
		
		if !fp.ShouldProtectFeature(feature, currentState, evolutionPressure) {
			candidates = append(candidates, feature)
		}
	}
	
	// 2. 按优先级排序（质量低、匹配少、年龄新的优先删除）
	fp.sortByEvolutionPriority(candidates)
	
	// 3. 计算需要删除的数量
	needToDelete := len(allFeatures) - targetCount
	if needToDelete <= 0 {
		return []*ProtectedFeature{}
	}
	
	if len(candidates) < needToDelete {
		log.Printf("Warning: only %d candidates available for evolution, need %d", 
			len(candidates), needToDelete)
		return candidates
	}
	
	return candidates[:needToDelete]
}

// calculateEvolutionPressure 计算演化压力
func (fp *FeatureProtector) calculateEvolutionPressure(currentCount, targetCount int) int {
	if currentCount <= targetCount {
		return 1 // 无压力
	}
	
	excess := currentCount - targetCount
	pressure := 1 + (excess * 4 / targetCount) // 超出越多，压力越大
	
	if pressure > 5 {
		pressure = 5
	}
	
	return pressure
}

// sortByEvolutionPriority 按演化优先级排序
func (fp *FeatureProtector) sortByEvolutionPriority(features []*ProtectedFeature) {
	// 简单的排序逻辑：质量低、匹配少、年龄新的排在前面
	for i := 0; i < len(features)-1; i++ {
		for j := i + 1; j < len(features); j++ {
			if fp.shouldDeleteFirst(features[i], features[j]) {
				features[i], features[j] = features[j], features[i]
			}
		}
	}
}

// shouldDeleteFirst 判断是否应该优先删除第一个特征
func (fp *FeatureProtector) shouldDeleteFirst(a, b *ProtectedFeature) bool {
	// 1. 临时特征优先删除
	if a.Tier == TierTemporary && b.Tier != TierTemporary {
		return true
	}
	if b.Tier == TierTemporary && a.Tier != TierTemporary {
		return false
	}
	
	// 2. 质量低的优先删除
	if a.QualityScore < b.QualityScore-0.1 {
		return true
	}
	if b.QualityScore < a.QualityScore-0.1 {
		return false
	}
	
	// 3. 匹配次数少的优先删除
	if a.MatchCount < b.MatchCount-5 {
		return true
	}
	if b.MatchCount < a.MatchCount-5 {
		return false
	}
	
	// 4. 年龄新的优先删除（保留历史特征）
	return a.CreatedAt.After(b.CreatedAt)
}

// CreateEvolutionPlan 创建演化计划
func (fp *FeatureProtector) CreateEvolutionPlan(
	allFeatures []*ProtectedFeature,
	currentState *AppearanceState,
	targetCount int,
) *EvolutionPlan {
	
	plan := &EvolutionPlan{
		Timestamp:     time.Now(),
		CurrentState:  currentState,
		TotalFeatures: len(allFeatures),
		TargetCount:   targetCount,
	}
	
	// 分类特征
	for _, feature := range allFeatures {
		switch feature.Tier {
		case TierCore:
			plan.CoreFeatures = append(plan.CoreFeatures, feature)
		case TierBaseline:
			plan.BaselineFeatures = append(plan.BaselineFeatures, feature)
		case TierAdaptive:
			plan.AdaptiveFeatures = append(plan.AdaptiveFeatures, feature)
		case TierTemporary:
			plan.TemporaryFeatures = append(plan.TemporaryFeatures, feature)
		}
	}
	
	// 获取删除候选
	plan.DeletionCandidates = fp.GetEvolutionCandidates(allFeatures, currentState, targetCount)
	plan.DeletionCount = len(plan.DeletionCandidates)
	
	// 计算保护统计
	plan.ProtectedCount = len(allFeatures) - plan.DeletionCount
	
	return plan
}

// EvolutionPlan 演化计划
type EvolutionPlan struct {
	Timestamp          time.Time           `json:"timestamp"`
	CurrentState       *AppearanceState    `json:"current_state"`
	TotalFeatures      int                 `json:"total_features"`
	TargetCount        int                 `json:"target_count"`
	DeletionCount      int                 `json:"deletion_count"`
	ProtectedCount     int                 `json:"protected_count"`
	CoreFeatures       []*ProtectedFeature `json:"core_features"`
	BaselineFeatures   []*ProtectedFeature `json:"baseline_features"`
	AdaptiveFeatures   []*ProtectedFeature `json:"adaptive_features"`
	TemporaryFeatures  []*ProtectedFeature `json:"temporary_features"`
	DeletionCandidates []*ProtectedFeature `json:"deletion_candidates"`
}

// LogEvolutionPlan 记录演化计划
func (fp *FeatureProtector) LogEvolutionPlan(plan *EvolutionPlan) {
	log.Printf("Evolution Plan Summary:")
	log.Printf("  Total features: %d → %d (delete %d)", 
		plan.TotalFeatures, plan.TargetCount, plan.DeletionCount)
	log.Printf("  Core: %d, Baseline: %d, Adaptive: %d, Temporary: %d",
		len(plan.CoreFeatures), len(plan.BaselineFeatures), 
		len(plan.AdaptiveFeatures), len(plan.TemporaryFeatures))
	log.Printf("  Current state: %s (should_protect: %v)",
		plan.CurrentState.State, plan.CurrentState.ShouldProtect)
}
