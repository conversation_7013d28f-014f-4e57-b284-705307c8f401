package shadow

import (
	"context"
	"fmt"
	"log"
	"sort"
	"time"

	"caby-ai/config"
	"caby-ai/pkg/qdrant"
	"caby-ai/pkg/vision"
)

// CatStateInfo 猫咪状态信息
type CatStateInfo struct {
	CatID       string `json:"cat_id"`
	State       string `json:"state"` // "medical", "clothing", "grooming", "other"
	Description string `json:"description"`
	StartTime   string `json:"start_time"` // ISO8601 formatted time string
	EndTime     string `json:"end_time"`   // ISO8601 formatted time string (expected)
}

// Service 影子模式服务
type Service struct {
	qdrantClient     *qdrant.QdrantClient
	visionClient     *vision.FeaturedClient
	config           *config.Config
	evolutionManager *FeatureEvolutionManager
	simpleProtector  *SimpleProtector
	stateClient      *StateClient
}

// SimilarityConfig 相似度配置
type SimilarityConfig struct {
	Threshold       float64 `json:"threshold"`         // 相似度阈值
	NewCatThreshold float64 `json:"new_cat_threshold"` // 新猫阈值
	TopK            int     `json:"top_k"`             // 返回前K个结果
}

// ShadowResult 影子模式识别结果
type ShadowResult struct {
	CatID          string  `json:"cat_id"`          // 识别到的猫咪ID，可能是"TBD"或"UNCERTAIN"
	Confidence     float64 `json:"confidence"`      // 识别置信度
	Similarity     float64 `json:"similarity"`      // 最高相似度
	IsNewCat       bool    `json:"is_new_cat"`      // 是否可能是新猫
	MatchedCatID   string  `json:"matched_cat_id"`  // 匹配到的猫咪ID（如果有）
	ModelVersion   string  `json:"model_version"`   // 模型版本
	FeaturesStored bool    `json:"features_stored"` // 特征是否已存储
	ProcessingTime int64   `json:"processing_time"` // 处理时间（毫秒）
}

// ShadowModeResult 影子模式结果（简化版，只保留重要信息）
type ShadowModeResult struct {
	OriginalResult string  `json:"original_result"`
	ShadowResult   string  `json:"shadow_result"`
	Similarity     float64 `json:"similarity"`
	MatchedCatID   string  `json:"matched_cat_id"`
	ModelVersion   string  `json:"model_version"`
}

// CatFeature 猫咪特征数据
type CatFeature struct {
	CatID          string    `json:"cat_id"`
	CatName        string    `json:"cat_name"`
	UserID         string    `json:"user_id"`
	Features       []float64 `json:"features"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
	FeatureVersion string    `json:"feature_version"`
}

// NewService 创建新的影子模式服务
func NewService(qdrantClient *qdrant.QdrantClient, visionClient *vision.FeaturedClient, config *config.Config) *Service {
	// 创建演化管理器
	evolutionManager := NewFeatureEvolutionManager(&config.ShadowMode, qdrantClient)

	// 创建用户反馈检测器
	userFeedbackDetector := NewUserFeedbackDetector(config.BackendServerURL)

	// 创建简单保护器
	simpleProtector := NewSimpleProtector(&config.ShadowMode, qdrantClient, userFeedbackDetector)

	// 创建状态检查客户端
	stateClient := NewStateClient(config.BackendServerURL, config.BackendServiceToken)

	return &Service{
		qdrantClient:     qdrantClient,
		visionClient:     visionClient,
		config:           config,
		evolutionManager: evolutionManager,
		simpleProtector:  simpleProtector,
		stateClient:      stateClient,
	}
}

// GetSimilarityConfig 获取相似度配置
func (s *Service) GetSimilarityConfig(userID string) SimilarityConfig {
	// 从配置文件或数据库获取用户特定的配置
	// 这里使用默认配置
	return SimilarityConfig{
		Threshold:       0.85,
		NewCatThreshold: 0.70,
		TopK:            5,
	}
}

// EnsureUserCollection 确保用户的集合存在
func (s *Service) EnsureUserCollection(ctx context.Context, userID string) error {
	collectionName := fmt.Sprintf("cat_features_%s", userID)

	// 检查集合是否存在
	exists, err := s.qdrantClient.CollectionExists(ctx, collectionName)
	if err != nil {
		return fmt.Errorf("failed to check collection existence: %w", err)
	}

	if !exists {
		// 创建集合，假设特征向量维度为768
		err = s.qdrantClient.CreateCollection(ctx, collectionName, 768)
		if err != nil {
			return fmt.Errorf("failed to create collection: %w", err)
		}
		log.Printf("Created collection for user: %s", userID)
	}

	return nil
}

// StoreCatFeature 存储猫咪特征
func (s *Service) StoreCatFeature(ctx context.Context, catFeature CatFeature) error {
	collectionName := fmt.Sprintf("cat_features_%s", catFeature.UserID)

	// 确保集合存在
	if err := s.EnsureUserCollection(ctx, catFeature.UserID); err != nil {
		return fmt.Errorf("failed to ensure collection: %w", err)
	}

	// 准备向量数据和载荷
	payload := map[string]interface{}{
		"cat_id":          catFeature.CatID,
		"cat_name":        catFeature.CatName,
		"user_id":         catFeature.UserID,
		"created_at":      catFeature.CreatedAt.Format(time.RFC3339),
		"updated_at":      catFeature.UpdatedAt.Format(time.RFC3339),
		"feature_version": catFeature.FeatureVersion,
	}

	// 存储到Qdrant
	err := s.qdrantClient.UpsertVector(ctx, collectionName, catFeature.CatID, catFeature.Features, payload)
	if err != nil {
		return fmt.Errorf("failed to store cat feature: %w", err)
	}

	log.Printf("Stored cat feature for cat_id: %s, user_id: %s", catFeature.CatID, catFeature.UserID)
	return nil
}

// FindSimilarCats 查找相似的猫咪
func (s *Service) FindSimilarCats(ctx context.Context, userID string, features []float64, topK int) ([]qdrant.SearchResult, error) {
	collectionName := fmt.Sprintf("cat_features_%s", userID)

	// 确保集合存在
	if err := s.EnsureUserCollection(ctx, userID); err != nil {
		return nil, fmt.Errorf("failed to ensure collection: %w", err)
	}

	// 搜索相似向量
	results, err := s.qdrantClient.Search(ctx, collectionName, features, topK)
	if err != nil {
		return nil, fmt.Errorf("failed to search similar cats: %w", err)
	}

	return results, nil
}

// ProcessShadowMode 处理影子模式识别
func (s *Service) ProcessShadowMode(ctx context.Context, userID string, features []float64, originalResult string) (*ShadowModeResult, error) {
	config := s.GetSimilarityConfig(userID)

	// 查找相似的猫咪
	similarCats, err := s.FindSimilarCats(ctx, userID, features, config.TopK)
	if err != nil {
		return nil, fmt.Errorf("failed to find similar cats: %w", err)
	}

	result := &ShadowModeResult{
		OriginalResult: originalResult,
		ModelVersion:   "featured_v1.0",
	}

	if len(similarCats) == 0 {
		// 没有找到相似的猫咪，判断为新猫
		result.ShadowResult = s.generateNewCatName()
		result.Similarity = 0.0
	} else {
		// 找到相似的猫咪
		bestMatch := similarCats[0]
		result.Similarity = bestMatch.Score

		if bestMatch.Score >= config.Threshold {
			// 相似度足够高，认为是已知猫咪
			// 直接使用cat_id作为结果
			if catID, ok := bestMatch.Payload["cat_id"].(string); ok {
				result.MatchedCatID = catID
				result.ShadowResult = catID // 直接使用cat_id，避免重名问题
			} else {
				// 如果没有cat_id，使用数字ID转换为字符串
				result.MatchedCatID = fmt.Sprintf("%v", bestMatch.ID)
				result.ShadowResult = result.MatchedCatID
			}
		} else if bestMatch.Score >= config.NewCatThreshold {
			// 相似度中等，可能是新猫但不确定
			result.ShadowResult = s.generateNewCatName()
		} else {
			// 相似度太低，判断为新猫
			result.ShadowResult = s.generateNewCatName()
		}
	}

	return result, nil
}

// generateNewCatName 生成新猫咪名称
func (s *Service) generateNewCatName() string {
	timestamp := time.Now().Format("20060102150405")
	return fmt.Sprintf("NewCat%s", timestamp)
}

// UpdateCatFeature 更新猫咪特征
func (s *Service) UpdateCatFeature(ctx context.Context, catFeature CatFeature) error {
	// 更新时间戳
	catFeature.UpdatedAt = time.Now()

	// 调用存储方法（Qdrant的upsert会自动更新）
	return s.StoreCatFeature(ctx, catFeature)
}

// DeleteCatFeature 删除猫咪特征
func (s *Service) DeleteCatFeature(ctx context.Context, userID, catID string) error {
	collectionName := fmt.Sprintf("cat_features_%s", userID)

	err := s.qdrantClient.DeleteVector(ctx, collectionName, catID)
	if err != nil {
		return fmt.Errorf("failed to delete cat feature: %w", err)
	}

	log.Printf("Deleted cat feature for cat_id: %s, user_id: %s", catID, userID)
	return nil
}

// GetUserCatFeatures 获取用户的所有猫咪特征
func (s *Service) GetUserCatFeatures(ctx context.Context, userID string) ([]CatFeature, error) {
	_ = fmt.Sprintf("cat_features_%s", userID) // 暂时未使用

	// 这里需要实现获取集合中所有向量的功能
	// Qdrant的HTTP API可能需要分页获取
	// 暂时返回空列表，实际实现时需要调用Qdrant的scroll API

	return []CatFeature{}, nil
}

// HealthCheck 健康检查
func (s *Service) HealthCheck(ctx context.Context) error {
	return s.qdrantClient.HealthCheck(ctx)
}

// ProcessImage 处理图像进行影子模式识别
func (s *Service) ProcessImage(ctx context.Context, imageBase64, userID, videoID string, catStates []CatStateInfo) (*ShadowResult, error) {
	startTime := time.Now()

	// 1. 提取特征向量
	featuresResp, err := s.visionClient.ExtractFeatures(imageBase64, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to extract features: %w", err)
	}

	if !featuresResp.Success {
		return nil, fmt.Errorf("feature extraction failed: %s", featuresResp.Error)
	}

	// 2. 确保用户的特征集合存在
	err = s.qdrantClient.EnsureUserCatFeaturesCollection(ctx, userID, featuresResp.Results.FeatureDim)
	if err != nil {
		return nil, fmt.Errorf("failed to ensure user collection: %w", err)
	}

	// 3. 搜索相似特征
	searchResults, err := s.qdrantClient.SearchSimilarCatFeatures(ctx, userID, featuresResp.Results.Features, s.config.ShadowMode.TopK)
	if err != nil {
		return nil, fmt.Errorf("failed to search similar features: %w", err)
	}

	// 4. 分析搜索结果
	result := s.analyzeSearchResults(searchResults, featuresResp)

	// 5. 存储特征向量（用于后续学习）
	if s.config.ShadowMode.StoreFeatures {
		err = s.storeFeatures(ctx, userID, videoID, featuresResp.Results.Features, result, featuresResp.Results.ModelVersion, catStates)
		if err != nil {
			log.Printf("Warning: failed to store features: %v", err)
			result.FeaturesStored = false
		} else {
			result.FeaturesStored = true
		}
	} else {
		log.Printf("Feature storage disabled by configuration")
		result.FeaturesStored = false
	}

	result.ProcessingTime = time.Since(startTime).Milliseconds()
	return result, nil
}

// analyzeSearchResults 分析搜索结果
func (s *Service) analyzeSearchResults(searchResults []qdrant.SearchResult, featuresResp *vision.FeaturedResponse) *ShadowResult {
	result := &ShadowResult{
		ModelVersion: featuresResp.Results.ModelVersion,
		IsNewCat:     false,
	}

	// 如果没有搜索结果，可能是新猫
	if len(searchResults) == 0 {
		result.CatID = "TBD"
		result.Confidence = 0.0
		result.Similarity = 0.0
		result.IsNewCat = true
		return result
	}

	// 获取最高相似度结果
	topResult := searchResults[0]
	result.Similarity = float64(topResult.Score)

	// 从payload中提取cat_id
	if catID, ok := topResult.Payload["cat_id"].(string); ok {
		result.MatchedCatID = catID
	}

	// 根据相似度阈值判断
	if result.Similarity >= s.config.ShadowMode.SimilarityThreshold {
		// 高相似度，认为是已知猫咪
		result.CatID = result.MatchedCatID
		result.Confidence = result.Similarity
		result.IsNewCat = false
	} else if result.Similarity <= s.config.ShadowMode.NewCatThreshold {
		// 低相似度，可能是新猫
		result.CatID = "TBD"
		result.Confidence = 1.0 - result.Similarity // 新猫的置信度
		result.IsNewCat = true
	} else {
		// 中等相似度，不确定
		result.CatID = "UNCERTAIN"
		result.Confidence = 0.5
		result.IsNewCat = false
	}

	return result
}

// storeFeatures 存储特征向量
func (s *Service) storeFeatures(ctx context.Context, userID, videoID string, features []float64, result *ShadowResult, modelVersion string, catStates []CatStateInfo) error {
	// 检查是否满足存储阈值
	if result.Similarity < s.config.ShadowMode.StoreThreshold {
		log.Printf("Skipping feature storage: similarity %.4f below threshold %.4f",
			result.Similarity, s.config.ShadowMode.StoreThreshold)
		return nil
	}

	// 检查猫咪是否处于特殊状态（医疗、穿衣等）
	catID := result.CatID
	if catID != "TBD" && catID != "UNCERTAIN" && catID != "unknown" {
		// 查找该猫咪是否有活跃状态
		for _, state := range catStates {
			if state.CatID == catID {
				log.Printf("Cat %s is in special state: %s, marking feature as protected",
					catID, state.State)

				// 将特征标记为受保护状态
				payload := map[string]interface{}{
					"video_id":          videoID,
					"model_version":     modelVersion,
					"timestamp":         time.Now().Format(time.RFC3339),
					"is_reference":      false, // 分析产生的特征，非参考特征
					"confidence":        result.Confidence,
					"source":            "analysis",
					"similarity":        result.Similarity,
					"matched_cat_id":    result.MatchedCatID,
					"is_protected":      true,
					"protection_reason": state.State,
					"protection_until":  state.EndTime,
				}

				// 存储受保护的特征
				return s.qdrantClient.UpsertCatFeature(ctx, userID, catID, features, payload)
			}
		}
	}

	payload := map[string]interface{}{
		"video_id":       videoID,
		"model_version":  modelVersion,
		"timestamp":      time.Now().Format(time.RFC3339),
		"is_reference":   false, // 分析产生的特征，非参考特征
		"confidence":     result.Confidence,
		"source":         "analysis",
		"similarity":     result.Similarity,
		"matched_cat_id": result.MatchedCatID,
	}

	// 如果识别出了具体的猫咪，使用该cat_id
	if catID == "TBD" || catID == "UNCERTAIN" {
		catID = "unknown" // 临时使用unknown作为cat_id
	}

	log.Printf("Storing features for cat_id: %s, similarity: %.4f, user_id: %s",
		catID, result.Similarity, userID)

	// 存储特征向量
	err := s.qdrantClient.UpsertCatFeature(ctx, userID, catID, features, payload)
	if err != nil {
		return err
	}

	// 检查是否需要进行特征演化（使用简单保护器）
	if s.config.ShadowMode.MaxFeaturesPerCat > 0 && s.config.ShadowMode.FeatureProtection {
		// 异步执行演化检查和执行，不阻塞主流程
		go func() {
			s.checkAndExecuteEvolution(context.Background(), userID, catID)
		}()
	}

	return nil
}

// checkAndExecuteEvolution 检查并执行特征演化（带保护机制）
func (s *Service) checkAndExecuteEvolution(ctx context.Context, userID, catID string) {
	// 1. 获取当前特征数量
	features, err := s.qdrantClient.SearchSimilarCatFeatures(ctx, userID, nil, 1000) // 获取所有特征
	if err != nil {
		log.Printf("Warning: failed to get features for evolution check: %v", err)
		return
	}

	currentCount := len(features)
	maxFeatures := s.config.ShadowMode.MaxFeaturesPerCat

	// 如果特征数量未超过限制，不需要演化
	if currentCount <= maxFeatures {
		log.Printf("Evolution not needed for cat %s: %d features (max: %d)",
			catID, currentCount, maxFeatures)
		return
	}

	// 2. 检查猫咪状态（如果状态客户端可用）
	if s.stateClient != nil {
		stateResp, err := s.stateClient.CheckCatState(ctx, userID, catID)
		if err != nil {
			log.Printf("Warning: failed to check cat state: %v", err)
		} else if stateResp != nil && stateResp.Success && stateResp.Data.HasState {
			// 如果猫咪处于特殊状态，暂停演化
			log.Printf("Evolution paused for cat %s: in %s state until %s",
				catID, stateResp.Data.State, stateResp.Data.ExpectedEndTime)
			return
		}
	}

	// 3. 使用简单保护器检查是否应该演化
	shouldEvolve, reason := s.simpleProtector.ShouldEvolve(ctx, userID, catID, currentCount)
	if !shouldEvolve {
		log.Printf("Evolution skipped for cat %s: %s", catID, reason)
		return
	}

	// 4. 获取可演化的特征
	simpleFeatures := s.simpleProtector.ConvertQdrantFeatures(features)
	candidates, err := s.simpleProtector.GetEvolutionCandidates(ctx, userID, catID, simpleFeatures, maxFeatures)
	if err != nil {
		log.Printf("Warning: failed to get evolution candidates: %v", err)
		return
	}

	if len(candidates) == 0 {
		log.Printf("No evolution candidates for cat %s", catID)
		return
	}

	// 5. 删除候选特征
	log.Printf("Evolving cat %s: deleting %d features to reach target of %d",
		catID, len(candidates), maxFeatures)

	// 实现批量删除
	deletedCount := 0
	for _, candidate := range candidates {
		// 实际删除特征（这里需要Qdrant支持按ID删除）
		log.Printf("Deleting feature %s (quality: %.3f)", candidate.ID, candidate.QualityScore)

		// 注意：这里需要实际的删除实现
		// err := s.qdrantClient.DeleteFeature(ctx, userID, candidate.ID)
		// if err != nil {
		//     log.Printf("Warning: failed to delete feature %s: %v", candidate.ID, err)
		//     continue
		// }

		deletedCount++
	}

	log.Printf("Successfully deleted %d features for cat %s", deletedCount, catID)
}

// cleanupOldFeatures 清理旧的特征向量，保持每只猫的特征数量在限制范围内
func (s *Service) cleanupOldFeatures(ctx context.Context, userID, catID string, maxFeatures int) error {
	log.Printf("Feature cleanup requested for cat %s (user %s), max features: %d",
		catID, userID, maxFeatures)

	// 1. 获取该猫咪的所有特征
	features, err := s.evolutionManager.getCatFeatures(ctx, userID, catID)
	if err != nil {
		log.Printf("Warning: failed to get cat features for cleanup: %v", err)
		return nil // 不返回错误，避免影响主流程
	}

	// 2. 检查是否需要清理
	if len(features) <= maxFeatures {
		log.Printf("Cat %s has %d features, no cleanup needed (max: %d)",
			catID, len(features), maxFeatures)
		return nil
	}

	// 3. 按时间戳排序，删除最旧的特征
	type featureWithTime struct {
		result    qdrant.SearchResult
		timestamp time.Time
	}

	var featuresWithTime []featureWithTime
	for _, feature := range features {
		if timestampStr, ok := feature.Payload["timestamp"].(string); ok {
			if timestamp, err := time.Parse(time.RFC3339, timestampStr); err == nil {
				featuresWithTime = append(featuresWithTime, featureWithTime{
					result:    feature,
					timestamp: timestamp,
				})
			}
		}
	}

	// 按时间排序（最旧的在前）
	sort.Slice(featuresWithTime, func(i, j int) bool {
		return featuresWithTime[i].timestamp.Before(featuresWithTime[j].timestamp)
	})

	// 4. 删除多余的特征
	toDelete := len(featuresWithTime) - maxFeatures
	deletedCount := 0

	for i := 0; i < toDelete && i < len(featuresWithTime); i++ {
		feature := featuresWithTime[i]
		log.Printf("Cleaning up old feature from %v (ID: %v)",
			feature.timestamp, feature.result.ID)

		// 注意：这里需要实际的删除实现
		// err := s.qdrantClient.DeleteFeature(ctx, userID, feature.result.ID)
		// if err != nil {
		//     log.Printf("Warning: failed to delete old feature: %v", err)
		//     continue
		// }

		deletedCount++
	}

	log.Printf("Cleanup completed for cat %s: deleted %d old features, remaining: %d",
		catID, deletedCount, len(featuresWithTime)-deletedCount)

	return nil
}

// InitializeCatFeatures 初始化猫咪特征（用于三只初始猫咪）
func (s *Service) InitializeCatFeatures(ctx context.Context, userID, catID string, imageBase64 string) error {
	log.Printf("Initializing features for cat %s with image data length: %d", catID, len(imageBase64))

	// 提取特征向量
	featuresResp, err := s.visionClient.ExtractFeatures(imageBase64, userID)
	if err != nil {
		// 如果caby_vision不可用，使用默认配置创建集合
		log.Printf("Warning: caby_vision not available for cat %s, using default vector size: %v", catID, err)

		// 使用默认向量大小（通常是512或1024）
		defaultVectorSize := 512
		err = s.qdrantClient.EnsureUserCatFeaturesCollection(ctx, userID, defaultVectorSize)
		if err != nil {
			return fmt.Errorf("failed to ensure user collection with default size: %w", err)
		}

		// 创建一个虚拟的特征向量
		dummyFeatures := make([]float64, defaultVectorSize)
		for i := range dummyFeatures {
			dummyFeatures[i] = 0.1 // 使用小的非零值
		}

		// 存储虚拟特征
		payload := map[string]interface{}{
			"model_version": "dummy_v1.0",
			"timestamp":     time.Now().Format(time.RFC3339),
			"is_reference":  true,
			"confidence":    0.5, // 虚拟特征的置信度较低
			"source":        "initial_dummy",
			"note":          "Created when caby_vision was not available",
		}

		return s.qdrantClient.UpsertCatFeature(ctx, userID, catID, dummyFeatures, payload)
	}

	if !featuresResp.Success || featuresResp.Results == nil {
		errorMsg := "unknown error"
		if featuresResp.Error != nil {
			errorMsg = *featuresResp.Error
		}
		return fmt.Errorf("feature extraction failed for cat %s: %s", catID, errorMsg)
	}

	// 检查特征维度，如果为0则使用默认值
	vectorSize := featuresResp.Results.FeatureDim
	if vectorSize <= 0 {
		log.Printf("Warning: invalid feature dimension %d for cat %s, using default size 512", vectorSize, catID)
		vectorSize = 512
	}

	// 检查特征向量是否为空
	features := featuresResp.Results.Features
	if len(features) == 0 {
		log.Printf("Warning: empty features for cat %s, creating dummy features", catID)
		features = make([]float64, vectorSize)
		for i := range features {
			features[i] = 0.1 // 使用小的非零值
		}
	}

	// 确保用户的特征集合存在
	err = s.qdrantClient.EnsureUserCatFeaturesCollection(ctx, userID, vectorSize)
	if err != nil {
		return fmt.Errorf("failed to ensure user collection: %w", err)
	}

	// 存储参考特征
	payload := map[string]interface{}{
		"model_version": featuresResp.Results.ModelVersion,
		"timestamp":     time.Now().Format(time.RFC3339),
		"is_reference":  true, // 这是参考特征
		"confidence":    1.0,  // 参考特征置信度为1.0
		"source":        "initial",
	}

	return s.qdrantClient.UpsertCatFeature(ctx, userID, catID, features, payload)
}

// GetSimilarCats 获取相似的猫咪列表（用于调试和分析）
func (s *Service) GetSimilarCats(ctx context.Context, userID string, imageBase64 string, limit int) ([]qdrant.SearchResult, error) {
	// 提取特征向量
	featuresResp, err := s.visionClient.ExtractFeatures(imageBase64, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to extract features: %w", err)
	}

	if !featuresResp.Success || featuresResp.Results == nil {
		errorMsg := "unknown error"
		if featuresResp.Error != nil {
			errorMsg = *featuresResp.Error
		}
		return nil, fmt.Errorf("feature extraction failed: %s", errorMsg)
	}

	// 搜索相似特征
	return s.qdrantClient.SearchSimilarCatFeatures(ctx, userID, featuresResp.Results.Features, limit)
}

// IsEnabled 检查影子模式是否启用
func (s *Service) IsEnabled() bool {
	return s.config.ShadowMode.Enabled
}

// GetShadowModeConfig 获取影子模式配置
func (s *Service) GetShadowModeConfig() *config.ShadowModeConfig {
	return &s.config.ShadowMode
}

// CleanupUserData 清理用户数据
func (s *Service) CleanupUserData(ctx context.Context, userID string) error {
	collectionName := s.qdrantClient.GetUserCatFeaturesCollection(userID)
	return s.qdrantClient.DeleteCollection(ctx, collectionName)
}
