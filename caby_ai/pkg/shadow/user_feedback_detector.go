package shadow

import (
	"context"
	"log"
	"time"
)

// UserFeedbackDetector 基于用户反馈的状态检测器
type UserFeedbackDetector struct {
	// 可以连接到backend_server的API或数据库
}

// NewUserFeedbackDetector 创建用户反馈检测器
func NewUserFeedbackDetector() *UserFeedbackDetector {
	return &UserFeedbackDetector{}
}

// UserStateMarker 用户状态标记
type UserStateMarker struct {
	UserID      string     `json:"user_id"`
	CatID       string     `json:"cat_id"`
	State       string     `json:"state"` // "medical", "clothing", "grooming", "normal"
	StartTime   time.Time  `json:"start_time"`
	EndTime     *time.Time `json:"end_time"`    // nil表示还在进行中
	Description string     `json:"description"` // 用户描述
	AutoDetect  bool       `json:"auto_detect"` // 是否自动检测结束
}

// DetectStateFromFeedback 基于用户反馈检测状态
func (ufd *UserFeedbackDetector) DetectStateFromFeedback(ctx context.Context, userID, catID string) (*AppearanceState, error) {
	// 1. 查询用户是否有活跃的状态标记
	activeMarker, err := ufd.getActiveStateMarker(ctx, userID, catID)
	if err != nil {
		log.Printf("Warning: failed to get active state marker: %v", err)
		return &AppearanceState{State: "normal", Confidence: 0.5}, nil
	}

	if activeMarker == nil {
		// 没有活跃标记，认为是正常状态
		return &AppearanceState{
			State:         "normal",
			Confidence:    0.8,
			IsTemporary:   false,
			ShouldProtect: false,
		}, nil
	}

	// 2. 基于用户标记返回状态
	state := &AppearanceState{
		State:           "abnormal",
		Confidence:      0.9, // 用户反馈的置信度很高
		AbnormalityType: activeMarker.State,
		IsTemporary:     true,
		ShouldProtect:   true, // 用户标记的异常状态需要保护正常特征
	}

	// 3. 计算持续时间
	duration := time.Since(activeMarker.StartTime)
	state.Duration = int(duration.Hours() / 24)

	// 4. 如果持续时间过长，可能不是临时状态
	if state.Duration > 60 { // 超过60天
		state.IsTemporary = false
		state.ShouldProtect = false
	}

	log.Printf("User feedback state for cat %s: %s (duration: %d days)",
		catID, activeMarker.State, state.Duration)

	return state, nil
}

// 这些方法需要实际的数据存储实现
func (ufd *UserFeedbackDetector) getActiveStateMarker(ctx context.Context, userID, catID string) (*UserStateMarker, error) {
	// TODO: 从数据库或API获取活跃的状态标记
	// 这里返回nil表示没有活跃标记
	return nil, nil
}

// MarkCatState 用户标记猫咪状态（API接口）
func (ufd *UserFeedbackDetector) MarkCatState(ctx context.Context, userID, catID, state, description string) error {
	marker := &UserStateMarker{
		UserID:      userID,
		CatID:       catID,
		State:       state,
		StartTime:   time.Now(),
		Description: description,
		AutoDetect:  true,
	}

	// TODO: 保存到数据库
	log.Printf("User marked cat %s as %s: %s (marker created)", catID, state, description)
	_ = marker // 避免未使用变量警告

	return nil
}

// EndCatState 结束猫咪状态标记
func (ufd *UserFeedbackDetector) EndCatState(ctx context.Context, userID, catID string) error {
	// TODO: 更新数据库，设置结束时间
	log.Printf("User ended state marking for cat %s", catID)

	return nil
}
