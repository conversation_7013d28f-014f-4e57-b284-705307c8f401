package shadow

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"time"
)

// UserFeedbackDetector 基于用户反馈的状态检测器
type UserFeedbackDetector struct {
	backendServerURL string
	httpClient       *http.Client
}

// NewUserFeedbackDetector 创建用户反馈检测器
func NewUserFeedbackDetector(backendServerURL string) *UserFeedbackDetector {
	return &UserFeedbackDetector{
		backendServerURL: backendServerURL,
		httpClient: &http.Client{
			Timeout: 10 * time.Second,
		},
	}
}

// UserStateMarker 用户状态标记
type UserStateMarker struct {
	UserID      string     `json:"user_id"`
	CatID       string     `json:"cat_id"`
	State       string     `json:"state"` // "medical", "clothing", "grooming", "normal"
	StartTime   time.Time  `json:"start_time"`
	EndTime     *time.Time `json:"end_time"`    // nil表示还在进行中
	Description string     `json:"description"` // 用户描述
	AutoDetect  bool       `json:"auto_detect"` // 是否自动检测结束
}

// DetectStateFromFeedback 基于用户反馈检测状态
func (ufd *UserFeedbackDetector) DetectStateFromFeedback(ctx context.Context, userID, catID string) (*AppearanceState, error) {
	// 1. 查询用户是否有活跃的状态标记
	activeMarker, err := ufd.getActiveStateMarker(ctx, userID, catID)
	if err != nil {
		log.Printf("Warning: failed to get active state marker: %v", err)
		return &AppearanceState{State: "normal", Confidence: 0.5}, nil
	}

	if activeMarker == nil {
		// 没有活跃标记，认为是正常状态
		return &AppearanceState{
			State:         "normal",
			Confidence:    0.8,
			IsTemporary:   false,
			ShouldProtect: false,
		}, nil
	}

	// 2. 基于用户标记返回状态
	state := &AppearanceState{
		State:           "abnormal",
		Confidence:      0.9, // 用户反馈的置信度很高
		AbnormalityType: activeMarker.State,
		IsTemporary:     true,
		ShouldProtect:   true, // 用户标记的异常状态需要保护正常特征
	}

	// 3. 计算持续时间
	duration := time.Since(activeMarker.StartTime)
	state.Duration = int(duration.Hours() / 24)

	// 4. 如果持续时间过长，可能不是临时状态
	if state.Duration > 60 { // 超过60天
		state.IsTemporary = false
		state.ShouldProtect = false
	}

	log.Printf("User feedback state for cat %s: %s (duration: %d days)",
		catID, activeMarker.State, state.Duration)

	return state, nil
}

// getActiveStateMarker 从backend_server获取活跃的状态标记
func (ufd *UserFeedbackDetector) getActiveStateMarker(ctx context.Context, userID, catID string) (*UserStateMarker, error) {
	if ufd.backendServerURL == "" {
		log.Printf("Backend server URL not configured, skipping state check")
		return nil, nil
	}

	// 构建API URL
	url := fmt.Sprintf("%s/api/cats/%s/state", ufd.backendServerURL, catID)

	// 创建请求
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// 添加用户ID头部（模拟认证）
	req.Header.Set("X-User-ID", userID)
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	resp, err := ufd.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	// 解析响应
	var apiResponse struct {
		Success bool `json:"success"`
		Data    struct {
			CatID            string `json:"cat_id"`
			HasState         bool   `json:"has_state"`
			State            string `json:"state"`
			Description      string `json:"description"`
			StartTime        string `json:"start_time"`
			ExpectedEndTime  string `json:"expected_end_time"`
			ExpectedDuration int    `json:"expected_duration"`
			IsActive         bool   `json:"is_active"`
		} `json:"data"`
	}

	if err := json.Unmarshal(body, &apiResponse); err != nil {
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	// 检查是否有活跃状态
	if !apiResponse.Success || !apiResponse.Data.HasState || !apiResponse.Data.IsActive {
		return nil, nil // 没有活跃状态
	}

	// 解析时间
	startTime, err := time.Parse(time.RFC3339, apiResponse.Data.StartTime)
	if err != nil {
		log.Printf("Warning: failed to parse start time: %v", err)
		startTime = time.Now()
	}

	var endTime *time.Time
	if apiResponse.Data.ExpectedEndTime != "" {
		if et, err := time.Parse(time.RFC3339, apiResponse.Data.ExpectedEndTime); err == nil {
			endTime = &et
		}
	}

	// 构建状态标记
	marker := &UserStateMarker{
		UserID:      userID,
		CatID:       catID,
		State:       apiResponse.Data.State,
		StartTime:   startTime,
		EndTime:     endTime,
		Description: apiResponse.Data.Description,
		AutoDetect:  true,
	}

	log.Printf("Retrieved active state for cat %s: %s (started: %v)",
		catID, marker.State, marker.StartTime)

	return marker, nil
}

// MarkCatState 用户标记猫咪状态（API接口）
func (ufd *UserFeedbackDetector) MarkCatState(ctx context.Context, userID, catID, state, description string) error {
	if ufd.backendServerURL == "" {
		log.Printf("Backend server URL not configured, cannot mark state")
		return fmt.Errorf("backend server not configured")
	}

	// 构建请求数据
	requestData := map[string]interface{}{
		"state":                  state,
		"description":            description,
		"expected_duration_days": 14, // 默认14天
	}

	jsonData, err := json.Marshal(requestData)
	if err != nil {
		return fmt.Errorf("failed to marshal request data: %w", err)
	}

	// 构建API URL
	url := fmt.Sprintf("%s/api/cats/%s/state", ufd.backendServerURL, catID)

	// 创建请求
	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	// 添加头部
	req.Header.Set("X-User-ID", userID)
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	resp, err := ufd.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	log.Printf("Successfully marked cat %s as %s: %s", catID, state, description)
	return nil
}

// EndCatState 结束猫咪状态标记
func (ufd *UserFeedbackDetector) EndCatState(ctx context.Context, userID, catID string) error {
	if ufd.backendServerURL == "" {
		log.Printf("Backend server URL not configured, cannot end state")
		return fmt.Errorf("backend server not configured")
	}

	// 构建API URL
	url := fmt.Sprintf("%s/api/cats/%s/state", ufd.backendServerURL, catID)

	// 创建请求
	req, err := http.NewRequestWithContext(ctx, "DELETE", url, nil)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	// 添加头部
	req.Header.Set("X-User-ID", userID)
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	resp, err := ufd.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	log.Printf("Successfully ended state marking for cat %s", catID)
	return nil
}
