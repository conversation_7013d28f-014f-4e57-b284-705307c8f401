# docker-compose.yml for caby_ai

services:
  qdrant:
    image: qdrant/qdrant:v1.7.4
    container_name: caby_qdrant
    ports:
      - "6333:6333" # REST API
      - "6334:6334" # gRPC API
    environment:
      QDRANT__SERVICE__HTTP_PORT: 6333
      QDRANT__SERVICE__GRPC_PORT: 6334
      QDRANT__LOG_LEVEL: INFO
      # 启用 API 密钥认证 (可选)
      QDRANT__SERVICE__API_KEY: ${QDRANT_API_KEY}
    volumes:
      - ./data/qdrant:/qdrant/storage
    networks:
      - caby_net
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6333/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s


  caby_ai:
    build:
      context: .
      dockerfile: Dockerfile.cabyai
      network: host
    container_name: caby_ai_service
    ports:
      - "8765:8765" # Expose port defined in config.yaml and Dockerfile
    volumes:
      # Mount config for easier changes without rebuilding image
      - ./config/config.yaml:/root/config/config.yaml:ro
      # Mount data volume if needed for temporary storage or logs
      # - ./data/logs:/root/logs
    environment:
      # Pass sensitive info via environment variables if preferred over config file
      # Ensure QDRANT_API_KEY is set in your environment or use the config file
      CABY_AI_SERVICE_TOKEN: ${CABY_AI_SERVICE_TOKEN}
      QDRANT_API_KEY: ${QDRANT_API_KEY}
      # Vision服务配置
      VISION_HOST: ${VISION_HOST}
      VISION_PORT: 8001
      VISION_API_KEY: ${VISION_API_KEY:-default_api_key}
      # Qdrant配置
      QDRANT_HOST: ${QDRANT_HOST:-qdrant}
      QDRANT_PORT: ${QDRANT_PORT:-6333}
      QDRANT_SCHEME: ${QDRANT_SCHEME:-http}
      # Backend服务配置 (用于发送影子模式结果)
      BACKEND_SERVER_URL: ${BACKEND_SERVER_URL:-http://backend_server:5678}
      BACKEND_SERVICE_TOKEN: ${BACKEND_SERVICE_TOKEN:-your_backend_service_token}
      # 影子模式配置
      SHADOW_MODE_ENABLED: ${SHADOW_MODE_ENABLED:-true}
      SHADOW_SIMILARITY_THRESHOLD: ${SHADOW_SIMILARITY_THRESHOLD:-0.85}
      SHADOW_NEW_CAT_THRESHOLD: ${SHADOW_NEW_CAT_THRESHOLD:-0.70}
      SHADOW_TOP_K: ${SHADOW_TOP_K:-5}
      GIN_MODE: release # Set Gin to release mode
      # 禁用代理以避免内部服务通信问题
      http_proxy: ""
      https_proxy: ""
      HTTP_PROXY: ""
      HTTPS_PROXY: ""
    depends_on:
      - qdrant
    networks:
      - caby_net
      # - backend_net # Add if needing to connect to backend_server network services
    restart: unless-stopped

volumes:
  qdrant_data:
    driver: local

networks:
  caby_net:
    driver: bridge
