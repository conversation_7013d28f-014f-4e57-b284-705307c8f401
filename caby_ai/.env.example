# =====================================
# 🤖 Caby AI - 视频分析服务环境变量配置
# =====================================
#
# 这是Caby AI项目的环境变量配置示例文件
# 复制此文件为 .env 并根据你的环境修改相应的值
#
# 使用方法：
# 1. cp .env.example .env
# 2. 编辑 .env 文件中的配置项
# 3. 运行服务
#

# =====================================
# 🚀 核心服务配置
# =====================================

# === 服务认证 ===
# 服务间通信认证令牌
CABY_AI_SERVICE_TOKEN=caby-token

# === Qdrant向量数据库配置 ===
# Qdrant服务地址
QDRANT_HOST=qdrant:6333
QDRANT_API_KEY=

# === Vision服务配置 ===
# Vision服务地址
VISION_HOST=caby_vision
VISION_API_KEY=default_api_key

# === Backend服务配置 ===
# Backend服务URL和认证令牌
BACKEND_SERVER_URL=https://api.caby.care
BACKEND_SERVICE_TOKEN=your_backend_service_token

# =====================================
# 🎯 影子模式配置
# =====================================

# 影子模式开关
SHADOW_MODE_ENABLED=true

# 相似度阈值 - 高于此值认为是已知猫咪
SHADOW_SIMILARITY_THRESHOLD=0.85

# 新猫阈值 - 低于此值认为是新猫咪
SHADOW_NEW_CAT_THRESHOLD=0.70

# 返回相似结果的数量
SHADOW_TOP_K=5

# =====================================
# 🧠 向量存储与学习配置
# =====================================

# 是否启用特征向量存储（用于持续学习）
# true: 每次识别都会存储特征向量到Qdrant，系统会持续学习
# false: 不存储特征向量，系统不会学习新特征
SHADOW_STORE_FEATURES=true

# 特征存储阈值 - 只存储相似度高于此值的特征
# 0.0: 存储所有特征（最大学习效果，但可能包含噪声）
# 0.3: 存储中等质量以上的特征（平衡学习效果和质量）
# 0.5: 只存储高质量特征（保证质量，但学习较慢）
SHADOW_STORE_THRESHOLD=0.3

# 每只猫最多存储的特征数量
# 建议值：200-500（平衡存储空间和学习效果）
# 100: 轻量级配置，适合资源受限环境
# 300: 推荐配置，平衡性能和学习效果
# 500: 高性能配置，最佳学习效果
# 1000+: 仅在充足资源和高精度要求时使用
SHADOW_MAX_FEATURES_PER_CAT=300

# 特征演化策略
# time_based: 基于时间的演化，保留最新特征
# quality_based: 基于质量的演化，保留高质量特征
# hybrid: 混合策略，综合考虑时间和质量
SHADOW_EVOLUTION_STRATEGY=hybrid

# 特征演化周期（小时）
# 系统会定期清理旧特征，保持特征库的新鲜度
# 24: 每天清理一次
# 168: 每周清理一次（推荐）
# 720: 每月清理一次
SHADOW_EVOLUTION_CYCLE=168

# 时间衰减因子（天）
# 控制旧特征权重的衰减速度
# 7: 一周内特征权重显著衰减（快速适应）
# 30: 一个月内特征权重逐渐衰减（推荐）
# 90: 三个月内特征权重缓慢衰减（保守策略）
SHADOW_TIME_DECAY_FACTOR=30.0

# 质量权重比例
# 在特征演化中质量因素的权重占比
# 0.5: 质量和时间因素各占50%
# 0.7: 质量因素占70%，时间因素占30%（推荐）
# 0.9: 主要基于质量进行演化
SHADOW_QUALITY_WEIGHT_RATIO=0.7

# =====================================
# 🔧 高级配置
# =====================================

# 服务监听地址
CABY_AI_LISTEN_ADDR=:8765

# 最大并发处理数
MAX_CONCURRENCY=2

# 请求超时时间（秒）
REQUEST_TIMEOUT=30

# =====================================
# 🐳 Docker部署配置
# =====================================

# Docker网络配置
DOCKER_NETWORK=caby_network

# 日志级别
LOG_LEVEL=info

# =====================================
# 📊 监控和调试
# =====================================

# 启用调试模式
DEBUG_MODE=false

# 性能监控
ENABLE_METRICS=true

# 日志输出格式 (json|text)
LOG_FORMAT=text
