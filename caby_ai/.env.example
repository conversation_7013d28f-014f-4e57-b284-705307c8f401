# =====================================
# 🤖 Caby AI - 视频分析服务环境变量配置
# =====================================
#
# 这是Caby AI项目的环境变量配置示例文件
# 复制此文件为 .env 并根据你的环境修改相应的值
#
# 使用方法：
# 1. cp .env.example .env
# 2. 编辑 .env 文件中的配置项
# 3. 运行服务
#

# =====================================
# 🚀 核心服务配置
# =====================================

# === 服务认证 ===
# 服务间通信认证令牌
CABY_AI_SERVICE_TOKEN=caby-token

# === Qdrant向量数据库配置 ===
# Qdrant服务地址
QDRANT_HOST=qdrant:6333
QDRANT_API_KEY=

# === Vision服务配置 ===
# Vision服务地址
VISION_HOST=caby_vision
VISION_API_KEY=default_api_key

# === Backend服务配置 ===
# Backend服务URL和认证令牌
BACKEND_SERVER_URL=https://api.caby.care
BACKEND_SERVICE_TOKEN=your_backend_service_token

# =====================================
# 🎯 影子模式配置
# =====================================

# 影子模式开关
SHADOW_MODE_ENABLED=true

# 相似度阈值 - 高于此值认为是已知猫咪
SHADOW_SIMILARITY_THRESHOLD=0.85

# 新猫阈值 - 低于此值认为是新猫咪
SHADOW_NEW_CAT_THRESHOLD=0.70

# 返回相似结果的数量
SHADOW_TOP_K=5

# =====================================
# 🔧 高级配置
# =====================================

# 服务监听地址
CABY_AI_LISTEN_ADDR=:8765

# 最大并发处理数
MAX_CONCURRENCY=2

# 请求超时时间（秒）
REQUEST_TIMEOUT=30

# =====================================
# 🐳 Docker部署配置
# =====================================

# Docker网络配置
DOCKER_NETWORK=caby_network

# 日志级别
LOG_LEVEL=info

# =====================================
# 📊 监控和调试
# =====================================

# 启用调试模式
DEBUG_MODE=false

# 性能监控
ENABLE_METRICS=true

# 日志输出格式 (json|text)
LOG_FORMAT=text
