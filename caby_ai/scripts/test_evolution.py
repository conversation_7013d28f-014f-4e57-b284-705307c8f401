#!/usr/bin/env python3
"""
特征演化机制测试脚本
验证特征的新陈代谢和渐进演化是否正常工作
"""

import requests
import json
import time
import os
from datetime import datetime, timedelta
from typing import Dict, List, Any

class EvolutionTest:
    def __init__(self, caby_ai_url: str = "http://localhost:8765", auth_token: str = ""):
        self.caby_ai_url = caby_ai_url
        self.auth_token = auth_token
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {auth_token}" if auth_token else ""
        }
    
    def simulate_feature_accumulation(self, user_id: str, cat_id: str, num_features: int = 50):
        """模拟特征积累过程"""
        print(f"Simulating feature accumulation for cat {cat_id}")
        print(f"Target: {num_features} features over time")
        
        results = []
        for i in range(num_features):
            # 模拟不同质量的特征
            similarity = 0.3 + (i % 10) * 0.07  # 0.3 到 0.93 之间变化
            
            # 创建模拟特征数据
            feature_data = {
                "user_id": user_id,
                "cat_id": cat_id,
                "features": [0.1 + i * 0.01] * 512,  # 模拟512维特征向量
                "similarity": similarity,
                "confidence": similarity,
                "timestamp": (datetime.now() - timedelta(days=i)).isoformat(),
                "source": "simulation"
            }
            
            # 这里应该调用实际的特征存储API
            # 由于当前API结构，我们记录模拟数据
            results.append({
                "feature_id": f"sim_{cat_id}_{i}",
                "similarity": similarity,
                "timestamp": feature_data["timestamp"],
                "quality_tier": self.classify_quality(similarity)
            })
            
            if i % 10 == 0:
                print(f"  Generated {i+1}/{num_features} features...")
        
        return results
    
    def classify_quality(self, similarity: float) -> str:
        """分类特征质量"""
        if similarity >= 0.8:
            return "high"
        elif similarity >= 0.6:
            return "medium"
        else:
            return "low"
    
    def analyze_evolution_strategy(self, features: List[Dict], max_features: int = 300):
        """分析不同演化策略的效果"""
        print(f"\nAnalyzing evolution strategies for {len(features)} features")
        print(f"Target: keep {max_features} features")
        
        strategies = {
            "time_based": self.simulate_time_based_evolution,
            "quality_based": self.simulate_quality_based_evolution,
            "hybrid": self.simulate_hybrid_evolution
        }
        
        results = {}
        for strategy_name, strategy_func in strategies.items():
            kept_features = strategy_func(features, max_features)
            results[strategy_name] = self.evaluate_strategy_result(kept_features)
            
            print(f"\n{strategy_name.upper()} Strategy Results:")
            print(f"  Kept features: {len(kept_features)}")
            print(f"  Avg quality: {results[strategy_name]['avg_quality']:.3f}")
            print(f"  Quality distribution: {results[strategy_name]['quality_dist']}")
            print(f"  Time span: {results[strategy_name]['time_span_days']:.1f} days")
        
        return results
    
    def simulate_time_based_evolution(self, features: List[Dict], max_features: int) -> List[Dict]:
        """模拟基于时间的演化策略"""
        # 按时间排序，保留最新的
        sorted_features = sorted(features, key=lambda x: x['timestamp'], reverse=True)
        return sorted_features[:max_features]
    
    def simulate_quality_based_evolution(self, features: List[Dict], max_features: int) -> List[Dict]:
        """模拟基于质量的演化策略"""
        # 按相似度排序，保留质量最高的
        sorted_features = sorted(features, key=lambda x: x['similarity'], reverse=True)
        return sorted_features[:max_features]
    
    def simulate_hybrid_evolution(self, features: List[Dict], max_features: int) -> List[Dict]:
        """模拟混合演化策略"""
        # 计算综合权重
        now = datetime.now()
        for feature in features:
            feature_time = datetime.fromisoformat(feature['timestamp'].replace('Z', '+00:00').replace('+00:00', ''))
            age_days = (now - feature_time).days
            
            # 时间权重（指数衰减）
            time_weight = 2.71828 ** (-age_days / 30.0)  # 30天衰减因子
            
            # 质量权重
            quality_weight = feature['similarity']
            
            # 综合权重（质量70%，时间30%）
            feature['hybrid_weight'] = quality_weight * 0.7 + time_weight * 0.3
        
        # 按综合权重排序
        sorted_features = sorted(features, key=lambda x: x['hybrid_weight'], reverse=True)
        return sorted_features[:max_features]
    
    def evaluate_strategy_result(self, features: List[Dict]) -> Dict[str, Any]:
        """评估策略结果"""
        if not features:
            return {"avg_quality": 0, "quality_dist": {}, "time_span_days": 0}
        
        # 平均质量
        avg_quality = sum(f['similarity'] for f in features) / len(features)
        
        # 质量分布
        quality_dist = {"high": 0, "medium": 0, "low": 0}
        for feature in features:
            quality_dist[self.classify_quality(feature['similarity'])] += 1
        
        # 时间跨度
        timestamps = [datetime.fromisoformat(f['timestamp'].replace('Z', '+00:00').replace('+00:00', '')) 
                     for f in features]
        time_span_days = (max(timestamps) - min(timestamps)).days if len(timestamps) > 1 else 0
        
        return {
            "avg_quality": avg_quality,
            "quality_dist": quality_dist,
            "time_span_days": time_span_days
        }
    
    def test_evolution_configuration(self):
        """测试演化配置的合理性"""
        print("=" * 60)
        print("FEATURE EVOLUTION CONFIGURATION TEST")
        print("=" * 60)
        
        # 读取当前配置
        config = self.read_evolution_config()
        print("Current Configuration:")
        for key, value in config.items():
            print(f"  {key}: {value}")
        
        # 分析配置合理性
        recommendations = self.analyze_config(config)
        
        print("\nConfiguration Analysis:")
        for rec in recommendations:
            print(f"  {rec}")
        
        return config, recommendations
    
    def read_evolution_config(self) -> Dict[str, Any]:
        """读取演化配置"""
        return {
            "SHADOW_STORE_FEATURES": os.getenv("SHADOW_STORE_FEATURES", "true"),
            "SHADOW_STORE_THRESHOLD": float(os.getenv("SHADOW_STORE_THRESHOLD", "0.3")),
            "SHADOW_MAX_FEATURES_PER_CAT": int(os.getenv("SHADOW_MAX_FEATURES_PER_CAT", "300")),
            "SHADOW_EVOLUTION_STRATEGY": os.getenv("SHADOW_EVOLUTION_STRATEGY", "hybrid"),
            "SHADOW_EVOLUTION_CYCLE": int(os.getenv("SHADOW_EVOLUTION_CYCLE", "168")),
            "SHADOW_TIME_DECAY_FACTOR": float(os.getenv("SHADOW_TIME_DECAY_FACTOR", "30.0")),
            "SHADOW_QUALITY_WEIGHT_RATIO": float(os.getenv("SHADOW_QUALITY_WEIGHT_RATIO", "0.7"))
        }
    
    def analyze_config(self, config: Dict[str, Any]) -> List[str]:
        """分析配置合理性"""
        recommendations = []
        
        # 检查存储阈值
        threshold = config["SHADOW_STORE_THRESHOLD"]
        if threshold < 0.2:
            recommendations.append("⚠️  Store threshold is very low, may include noise")
        elif threshold > 0.6:
            recommendations.append("⚠️  Store threshold is high, may miss learning opportunities")
        else:
            recommendations.append("✅ Store threshold is reasonable")
        
        # 检查特征数量
        max_features = config["SHADOW_MAX_FEATURES_PER_CAT"]
        if max_features < 100:
            recommendations.append("⚠️  Max features per cat is low, may limit learning")
        elif max_features > 500:
            recommendations.append("⚠️  Max features per cat is high, may impact performance")
        else:
            recommendations.append("✅ Max features per cat is reasonable")
        
        # 检查演化周期
        cycle_hours = config["SHADOW_EVOLUTION_CYCLE"]
        if cycle_hours < 24:
            recommendations.append("⚠️  Evolution cycle is very frequent, may be resource intensive")
        elif cycle_hours > 720:  # 30 days
            recommendations.append("⚠️  Evolution cycle is infrequent, may accumulate stale features")
        else:
            recommendations.append("✅ Evolution cycle is reasonable")
        
        # 检查衰减因子
        decay_factor = config["SHADOW_TIME_DECAY_FACTOR"]
        if decay_factor < 7:
            recommendations.append("⚠️  Time decay factor is low, features age quickly")
        elif decay_factor > 90:
            recommendations.append("⚠️  Time decay factor is high, old features persist long")
        else:
            recommendations.append("✅ Time decay factor is reasonable")
        
        return recommendations
    
    def run_comprehensive_test(self):
        """运行综合测试"""
        print("=" * 60)
        print("COMPREHENSIVE EVOLUTION MECHANISM TEST")
        print("=" * 60)
        
        # 1. 配置测试
        config, recommendations = self.test_evolution_configuration()
        
        # 2. 模拟特征积累
        print(f"\n{'-' * 40}")
        print("FEATURE ACCUMULATION SIMULATION")
        print(f"{'-' * 40}")
        
        test_user = "test_user_evolution"
        test_cat = "test_cat_001"
        
        features = self.simulate_feature_accumulation(test_user, test_cat, 400)
        
        # 3. 演化策略分析
        print(f"\n{'-' * 40}")
        print("EVOLUTION STRATEGY ANALYSIS")
        print(f"{'-' * 40}")
        
        max_features = config["SHADOW_MAX_FEATURES_PER_CAT"]
        strategy_results = self.analyze_evolution_strategy(features, max_features)
        
        # 4. 生成报告
        report = {
            "timestamp": datetime.now().isoformat(),
            "config": config,
            "recommendations": recommendations,
            "simulation_results": {
                "total_features_generated": len(features),
                "target_features": max_features,
                "strategy_comparison": strategy_results
            }
        }
        
        # 保存报告
        with open("/tmp/evolution_test_report.json", "w") as f:
            json.dump(report, f, indent=2)
        
        print(f"\n{'=' * 60}")
        print("TEST COMPLETED")
        print(f"{'=' * 60}")
        print(f"Report saved to: /tmp/evolution_test_report.json")
        
        return report

def main():
    tester = EvolutionTest()
    report = tester.run_comprehensive_test()
    
    # 输出关键结论
    print("\nKEY FINDINGS:")
    best_strategy = max(report["simulation_results"]["strategy_comparison"].items(),
                       key=lambda x: x[1]["avg_quality"])
    print(f"  Best strategy: {best_strategy[0]} (avg quality: {best_strategy[1]['avg_quality']:.3f})")
    
    high_quality_count = sum(1 for r in report["recommendations"] if r.startswith("✅"))
    total_checks = len(report["recommendations"])
    print(f"  Configuration health: {high_quality_count}/{total_checks} checks passed")

if __name__ == "__main__":
    main()
