#!/usr/bin/env python3
"""
测试向量存储机制的脚本
验证caby_ai是否正确将新的向量识别结果存储到Qdrant数据库
"""

import requests
import json
import base64
import time
import os
from typing import Dict, Any

class VectorStorageTest:
    def __init__(self, caby_ai_url: str = "http://localhost:8765", auth_token: str = ""):
        self.caby_ai_url = caby_ai_url
        self.auth_token = auth_token
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {auth_token}" if auth_token else ""
        }
    
    def test_shadow_mode_processing(self, user_id: str, test_image_path: str) -> Dict[str, Any]:
        """测试影子模式处理并检查向量存储"""
        print(f"Testing shadow mode processing for user: {user_id}")
        
        # 读取测试图片
        if not os.path.exists(test_image_path):
            print(f"Test image not found: {test_image_path}")
            return {"error": "Test image not found"}
        
        with open(test_image_path, 'rb') as f:
            image_data = f.read()
        
        image_base64 = base64.b64encode(image_data).decode('utf-8')
        
        # 调用影子模式API
        payload = {
            "image": image_base64,
            "user_id": user_id,
            "video_id": f"test_{int(time.time())}"
        }
        
        try:
            response = requests.post(
                f"{self.caby_ai_url}/api/shadow/process",
                json=payload,
                headers=self.headers,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"Shadow mode processing successful:")
                print(f"  - Cat ID: {result.get('cat_id', 'N/A')}")
                print(f"  - Similarity: {result.get('similarity', 'N/A')}")
                print(f"  - Features Stored: {result.get('features_stored', 'N/A')}")
                print(f"  - Model Version: {result.get('model_version', 'N/A')}")
                return result
            else:
                print(f"Shadow mode processing failed: {response.status_code}")
                print(f"Response: {response.text}")
                return {"error": f"HTTP {response.status_code}: {response.text}"}
                
        except requests.exceptions.RequestException as e:
            print(f"Request failed: {e}")
            return {"error": str(e)}
    
    def test_multiple_images(self, user_id: str, image_paths: list, interval: int = 2):
        """测试多张图片的处理，验证向量累积存储"""
        print(f"\nTesting multiple image processing for user: {user_id}")
        results = []
        
        for i, image_path in enumerate(image_paths):
            print(f"\n--- Processing image {i+1}/{len(image_paths)}: {image_path} ---")
            result = self.test_shadow_mode_processing(user_id, image_path)
            results.append(result)
            
            if i < len(image_paths) - 1:
                print(f"Waiting {interval} seconds before next image...")
                time.sleep(interval)
        
        return results
    
    def check_qdrant_collection(self, user_id: str) -> Dict[str, Any]:
        """检查Qdrant中用户的特征集合"""
        print(f"\nChecking Qdrant collection for user: {user_id}")
        
        try:
            # 这里需要直接访问Qdrant API或通过caby_ai的管理接口
            # 暂时返回占位符信息
            print("Note: Direct Qdrant collection check not implemented yet")
            print("You can check the Qdrant dashboard or logs to verify vector storage")
            return {"status": "check_manually"}
            
        except Exception as e:
            print(f"Failed to check Qdrant collection: {e}")
            return {"error": str(e)}
    
    def run_comprehensive_test(self, user_id: str, test_images: list):
        """运行综合测试"""
        print("=" * 60)
        print("CABY_AI VECTOR STORAGE MECHANISM TEST")
        print("=" * 60)
        
        print(f"Target caby_ai URL: {self.caby_ai_url}")
        print(f"Test user ID: {user_id}")
        print(f"Test images: {len(test_images)}")
        
        # 测试多张图片处理
        results = self.test_multiple_images(user_id, test_images)
        
        # 分析结果
        print("\n" + "=" * 60)
        print("TEST RESULTS SUMMARY")
        print("=" * 60)
        
        successful_processes = 0
        features_stored_count = 0
        
        for i, result in enumerate(results):
            if "error" not in result:
                successful_processes += 1
                if result.get("features_stored", False):
                    features_stored_count += 1
        
        print(f"Total images processed: {len(results)}")
        print(f"Successful processes: {successful_processes}")
        print(f"Features stored: {features_stored_count}")
        
        if features_stored_count > 0:
            print("\n✅ VECTOR STORAGE MECHANISM IS WORKING!")
            print("   New vector features are being stored to Qdrant database.")
        else:
            print("\n❌ VECTOR STORAGE MECHANISM MAY NOT BE WORKING!")
            print("   No features were stored. Check configuration and logs.")
        
        # 检查Qdrant集合
        self.check_qdrant_collection(user_id)
        
        return {
            "total_images": len(results),
            "successful_processes": successful_processes,
            "features_stored": features_stored_count,
            "results": results
        }

def main():
    # 配置测试参数
    CABY_AI_URL = os.getenv("CABY_AI_URL", "http://localhost:8765")
    AUTH_TOKEN = os.getenv("CABY_AI_AUTH_TOKEN", "")
    TEST_USER_ID = os.getenv("TEST_USER_ID", "test_user_123")
    
    # 测试图片路径（需要提供实际的图片文件）
    TEST_IMAGES = [
        "/tmp/test_cat1.jpg",  # 替换为实际的测试图片路径
        "/tmp/test_cat2.jpg",
        "/tmp/test_cat3.jpg"
    ]
    
    # 创建测试实例
    tester = VectorStorageTest(CABY_AI_URL, AUTH_TOKEN)
    
    # 运行测试
    results = tester.run_comprehensive_test(TEST_USER_ID, TEST_IMAGES)
    
    # 保存结果
    with open("/tmp/vector_storage_test_results.json", "w") as f:
        json.dump(results, f, indent=2)
    
    print(f"\nTest results saved to: /tmp/vector_storage_test_results.json")

if __name__ == "__main__":
    main()
