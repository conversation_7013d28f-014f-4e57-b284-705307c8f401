#!/bin/bash

# 部署特征保护机制脚本

echo "========================================"
echo "DEPLOYING FEATURE PROTECTION MECHANISM"
echo "========================================"

# 检查当前目录
if [ ! -f "config/config.yaml" ]; then
    echo "Error: Please run this script from caby_ai directory"
    exit 1
fi

# 1. 备份当前配置
echo "1. Backing up current configuration..."
cp .env.example .env.example.backup.$(date +%Y%m%d_%H%M%S)
cp config/config.yaml config/config.yaml.backup.$(date +%Y%m%d_%H%M%S)
echo "   ✅ Configuration backed up"

# 2. 验证新配置
echo "2. Validating new configuration..."

# 检查必要的环境变量
required_vars=(
    "SHADOW_MODE_ENABLED"
    "SHADOW_STORE_FEATURES" 
    "SHADOW_STORE_THRESHOLD"
    "SHADOW_MAX_FEATURES_PER_CAT"
    "SHADOW_EVOLUTION_CYCLE"
    "SHADOW_FEATURE_PROTECTION"
    "SHADOW_RECENT_DAYS_PROTECTION"
    "SHADOW_PROTECTION_PERCENTAGE"
)

missing_vars=()
for var in "${required_vars[@]}"; do
    if ! grep -q "^${var}=" .env.example; then
        missing_vars+=("$var")
    fi
done

if [ ${#missing_vars[@]} -gt 0 ]; then
    echo "   ❌ Missing required environment variables:"
    printf '      %s\n' "${missing_vars[@]}"
    exit 1
fi

echo "   ✅ Configuration validation passed"

# 3. 编译检查
echo "3. Checking code compilation..."
if ! go build -o /tmp/caby_ai_test ./cmd/main.go; then
    echo "   ❌ Compilation failed"
    exit 1
fi
rm -f /tmp/caby_ai_test
echo "   ✅ Code compilation successful"

# 4. 运行测试
echo "4. Running protection mechanism tests..."

# 检查配置
echo "   Testing configuration..."
if ! ./scripts/check_vector_config.sh > /tmp/config_check.log 2>&1; then
    echo "   ⚠️  Configuration check warnings (see /tmp/config_check.log)"
else
    echo "   ✅ Configuration check passed"
fi

# 5. 显示部署摘要
echo "5. Deployment Summary:"
echo "   ================================"

echo "   📊 Configuration Changes:"
echo "      - Store Threshold: 0.3 → 0.5 (higher quality)"
echo "      - Max Features: 300 → 250 (better performance)"  
echo "      - Evolution Cycle: 168h → 336h (2 weeks, safer)"
echo "      - Feature Protection: ENABLED"
echo "      - Recent Days Protection: 7 days"
echo "      - Protection Percentage: 20%"

echo ""
echo "   🛡️ Protection Features:"
echo "      - Time-based feature protection"
echo "      - User state marking API"
echo "      - Evolution pause during special states"
echo "      - Quality-based feature preservation"

echo ""
echo "   🔧 New Components:"
echo "      - SimpleProtector: Basic feature protection"
echo "      - StateClient: User state checking"
echo "      - CatStateHandler: State marking API"
echo "      - CatStateService: State management"

# 6. 部署建议
echo ""
echo "6. Deployment Recommendations:"
echo "   ================================"

echo "   📋 Immediate Actions:"
echo "      1. Update environment variables in production"
echo "      2. Restart caby_ai service"
echo "      3. Deploy backend_server state API"
echo "      4. Create cat_states database table"

echo ""
echo "   📋 Monitoring Setup:"
echo "      1. Monitor evolution frequency"
echo "      2. Track feature protection effectiveness"
echo "      3. Watch for user state marking usage"
echo "      4. Monitor system performance impact"

echo ""
echo "   📋 User Communication:"
echo "      1. Document new state marking feature"
echo "      2. Provide usage examples"
echo "      3. Set up support for state-related issues"

# 7. 生成部署清单
echo ""
echo "7. Generating deployment checklist..."

cat > /tmp/deployment_checklist.md << 'EOF'
# Feature Protection Deployment Checklist

## Pre-deployment
- [ ] Backup current configuration files
- [ ] Verify code compilation
- [ ] Run protection mechanism tests
- [ ] Review configuration changes

## Deployment Steps
- [ ] Update caby_ai environment variables
- [ ] Deploy updated caby_ai service
- [ ] Deploy backend_server state API
- [ ] Create cat_states database table
- [ ] Verify service connectivity

## Post-deployment Verification
- [ ] Check caby_ai logs for protection messages
- [ ] Test state marking API endpoints
- [ ] Verify evolution cycle changes
- [ ] Monitor feature count changes
- [ ] Test user state marking workflow

## Monitoring Setup
- [ ] Set up evolution frequency monitoring
- [ ] Configure protection effectiveness alerts
- [ ] Monitor system performance metrics
- [ ] Track user state marking usage

## Documentation
- [ ] Update API documentation
- [ ] Create user guide for state marking
- [ ] Document troubleshooting procedures
- [ ] Update system architecture docs

## Rollback Plan
- [ ] Keep backup configurations ready
- [ ] Document rollback procedures
- [ ] Test rollback in staging environment
- [ ] Prepare communication for rollback scenario
EOF

echo "   ✅ Deployment checklist saved to /tmp/deployment_checklist.md"

# 8. 生成测试命令
echo ""
echo "8. Testing Commands:"
echo "   ================================"

echo "   🧪 Configuration Test:"
echo "      ./scripts/check_vector_config.sh"

echo ""
echo "   🧪 State API Test (after backend deployment):"
echo "      # Mark cat state"
echo "      curl -X POST http://localhost:8080/api/cats/test_cat/state \\"
echo "           -H 'Content-Type: application/json' \\"
echo "           -H 'X-User-ID: test_user' \\"
echo "           -d '{\"state\":\"medical\",\"description\":\"Surgery recovery\",\"expected_duration_days\":14}'"
echo ""
echo "      # Check cat state"  
echo "      curl -X GET http://localhost:8080/api/cats/test_cat/state \\"
echo "           -H 'X-User-ID: test_user'"
echo ""
echo "      # End cat state"
echo "      curl -X DELETE http://localhost:8080/api/cats/test_cat/state \\"
echo "           -H 'X-User-ID: test_user'"

echo ""
echo "   🧪 Evolution Test:"
echo "      # Check evolution logs"
echo "      docker logs caby_ai | grep -i evolution"
echo ""
echo "      # Check protection logs"
echo "      docker logs caby_ai | grep -i protection"

echo ""
echo "========================================"
echo "DEPLOYMENT PREPARATION COMPLETE"
echo "========================================"
echo ""
echo "Next steps:"
echo "1. Review the deployment checklist: /tmp/deployment_checklist.md"
echo "2. Update production environment variables"
echo "3. Deploy the updated services"
echo "4. Run post-deployment tests"
echo ""
echo "For support, check the documentation:"
echo "- docs/OPTIMIZED_CONFIGURATION.md"
echo "- docs/FUTURE_IMPLEMENTATION_PLAN.md"
echo "- docs/VECTOR_STORAGE.md"
