#!/bin/bash

# 新功能快速测试脚本
# 专门测试 cat states 和 vector evolution 功能
# 本地IP: **********
# 注意：此脚本不负责构建Docker，只负责测试

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m'

# 配置
LOCAL_IP="**********"
CABY_AI_URL="http://${LOCAL_IP}:8765"
BACKEND_URL="http://${LOCAL_IP}:8080"

echo -e "${WHITE}🧪 Caby AI 新功能快速测试${NC}"
echo -e "${CYAN}测试目标: Cat States + Vector Evolution + Feature Protection${NC}"
echo -e "${YELLOW}注意: 请确保服务已通过 quick_deploy.sh 部署${NC}"
echo "=" * 60

# 函数定义
print_step() {
    echo -e "\n${BLUE}🔍 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# 检查服务状态
check_services() {
    print_step "检查服务状态"

    # 检查 Caby AI
    echo -e "${CYAN}检查 Caby AI 服务: ${CABY_AI_URL}${NC}"
    if curl -s --connect-timeout 5 "${CABY_AI_URL}/health" > /dev/null 2>&1; then
        print_success "Caby AI 服务运行正常"
    else
        print_error "Caby AI 服务不可访问"
        echo -e "${CYAN}请先运行: ./scripts/quick_deploy.sh${NC}"
        echo -e "${CYAN}或检查服务是否在端口 8081 上运行${NC}"
        exit 1
    fi

    # 检查 Backend Server
    echo -e "${CYAN}检查 Backend Server 服务: ${BACKEND_URL}${NC}"
    if curl -s --connect-timeout 5 "${BACKEND_URL}/health" > /dev/null 2>&1; then
        print_success "Backend Server 服务运行正常"
    else
        print_warning "Backend Server 不可访问"
        print_warning "Cat States 相关测试可能失败，但其他测试仍会继续"
    fi
}

# 运行单个测试
run_single_test() {
    local test_name=$1
    local test_description=$2

    print_step "测试 ${test_description}"

    echo -e "${CYAN}运行 ${test_name} 测试...${NC}"
    if python3 scripts/test.py "${test_name}" \
        --url "${CABY_AI_URL}" \
        --backend-url "${BACKEND_URL}" \
        --verbose; then
        print_success "${test_description} 测试通过"
        return 0
    else
        print_error "${test_description} 测试失败"
        return 1
    fi
}

# 运行基础健康检查
run_health_check() {
    print_step "运行基础健康检查"

    echo -e "${CYAN}运行健康检查测试...${NC}"
    if python3 scripts/test.py health vision_health \
        --url "${CABY_AI_URL}" \
        --backend-url "${BACKEND_URL}"; then
        print_success "基础健康检查通过"
        return 0
    else
        print_error "基础健康检查失败"
        return 1
    fi
}

# 检查测试结果
check_results() {
    print_step "检查测试结果"
    
    if [ -f "test_results.json" ]; then
        print_success "测试结果文件存在"
        
        # 显示新功能测试结果
        echo -e "${CYAN}新功能测试结果:${NC}"
        
        if command -v jq > /dev/null 2>&1; then
            # 使用 jq 解析结果
            for test in "cat_states" "vector_evolution" "feature_protection" "shadow_mode_enhanced"; do
                local result=$(jq -r ".$test.success // false" test_results.json 2>/dev/null)
                if [ "$result" = "true" ]; then
                    echo -e "  ✅ $test"
                elif [ "$result" = "false" ]; then
                    echo -e "  ❌ $test"
                else
                    echo -e "  ⚪ $test (未测试)"
                fi
            done
        else
            print_warning "安装 jq 以获得更好的结果显示"
        fi
    else
        print_warning "未找到测试结果文件"
    fi
}

# 显示日志摘要
show_logs() {
    print_step "显示相关日志"
    
    if [ -f "log/caby_ai.log" ]; then
        echo -e "${CYAN}最近的相关日志:${NC}"
        grep -E "(cat_states|vector|evolution|protection|shadow)" log/caby_ai.log | tail -10 || true
    else
        print_warning "未找到日志文件"
    fi
}

# 主执行流程
main() {
    # 检查当前目录
    if [ ! -f "scripts/test.py" ]; then
        print_error "请在 caby_ai 根目录下运行此脚本"
        exit 1
    fi

    # 执行测试
    check_services

    local success_count=0
    local total_tests=5

    # 运行基础健康检查
    if run_health_check; then
        ((success_count++))
    fi

    # 运行新功能测试
    if run_single_test "cat_states" "猫咪状态管理功能"; then
        ((success_count++))
    fi

    if run_single_test "vector_evolution" "向量演化功能"; then
        ((success_count++))
    fi

    if run_single_test "feature_protection" "特征保护机制"; then
        ((success_count++))
    fi

    if run_single_test "shadow_mode_enhanced" "增强影子模式"; then
        ((success_count++))
    fi

    # 检查结果
    check_results
    show_logs

    # 显示总结
    echo -e "\n${WHITE}📊 测试总结${NC}"
    echo -e "${CYAN}通过测试: ${success_count}/${total_tests}${NC}"

    if [ $success_count -eq $total_tests ]; then
        print_success "所有新功能测试通过！"
        echo -e "${GREEN}🎉 功能正常，可以继续开发或提交代码${NC}"
        exit 0
    elif [ $success_count -gt 2 ]; then
        print_warning "大部分测试通过，请检查失败的测试"
        echo -e "${YELLOW}💡 建议查看详细日志定位问题${NC}"
        exit 0  # 大部分通过也算成功
    else
        print_error "多数测试失败，请检查服务状态和配置"
        exit 1
    fi
}

# 运行主函数
main "$@"
