#!/bin/bash

# 新功能快速测试脚本
# 专门测试 cat states 和 vector evolution 功能
# 本地IP: **********

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m'

# 配置
LOCAL_IP="**********"
CABY_AI_URL="http://${LOCAL_IP}:8081"
BACKEND_URL="http://${LOCAL_IP}:8080"

echo -e "${WHITE}🧪 Caby AI 新功能快速测试${NC}"
echo -e "${CYAN}测试目标: Cat States + Vector Evolution${NC}"
echo "=" * 50

# 函数定义
print_step() {
    echo -e "\n${BLUE}🔍 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# 检查服务状态
check_services() {
    print_step "检查服务状态"
    
    # 检查 Caby AI
    if curl -s --connect-timeout 5 "${CABY_AI_URL}/health" > /dev/null 2>&1; then
        print_success "Caby AI 服务运行正常"
    else
        print_error "Caby AI 服务不可访问: ${CABY_AI_URL}"
        echo -e "${CYAN}请先运行: ./scripts/quick_deploy.sh${NC}"
        exit 1
    fi
    
    # 检查 Backend Server
    if curl -s --connect-timeout 5 "${BACKEND_URL}/health" > /dev/null 2>&1; then
        print_success "Backend Server 服务运行正常"
    else
        print_warning "Backend Server 不可访问: ${BACKEND_URL}"
        print_warning "Cat States 测试可能失败"
    fi
}

# 测试 Cat States 功能
test_cat_states() {
    print_step "测试猫咪状态管理功能"
    
    echo -e "${CYAN}运行 cat_states 测试...${NC}"
    if python3 scripts/test.py cat_states \
        --url "${CABY_AI_URL}" \
        --backend-url "${BACKEND_URL}" \
        --verbose; then
        print_success "Cat States 测试通过"
        return 0
    else
        print_error "Cat States 测试失败"
        return 1
    fi
}

# 测试 Vector Evolution 功能
test_vector_evolution() {
    print_step "测试向量演化功能"
    
    echo -e "${CYAN}运行 vector_evolution 测试...${NC}"
    if python3 scripts/test.py vector_evolution \
        --url "${CABY_AI_URL}" \
        --backend-url "${BACKEND_URL}" \
        --verbose; then
        print_success "Vector Evolution 测试通过"
        return 0
    else
        print_error "Vector Evolution 测试失败"
        return 1
    fi
}

# 测试特征保护机制
test_feature_protection() {
    print_step "测试特征保护机制"
    
    echo -e "${CYAN}运行 feature_protection 测试...${NC}"
    if python3 scripts/test.py feature_protection \
        --url "${CABY_AI_URL}" \
        --backend-url "${BACKEND_URL}" \
        --verbose; then
        print_success "Feature Protection 测试通过"
        return 0
    else
        print_error "Feature Protection 测试失败"
        return 1
    fi
}

# 测试增强影子模式
test_shadow_mode_enhanced() {
    print_step "测试增强影子模式"
    
    echo -e "${CYAN}运行 shadow_mode_enhanced 测试...${NC}"
    if python3 scripts/test.py shadow_mode_enhanced \
        --url "${CABY_AI_URL}" \
        --backend-url "${BACKEND_URL}" \
        --verbose; then
        print_success "Enhanced Shadow Mode 测试通过"
        return 0
    else
        print_error "Enhanced Shadow Mode 测试失败"
        return 1
    fi
}

# 运行集成测试
run_integration_test() {
    print_step "运行新功能集成测试"
    
    echo -e "${CYAN}测试完整的状态感知分析流程...${NC}"
    
    # 使用 curl 进行集成测试
    local test_data='{
        "video_id": "integration_test_'$(date +%s)'",
        "user_id": "test_user_001",
        "cat_states": [
            {
                "cat_id": "test_cat_001",
                "state": "medical",
                "description": "Integration test",
                "start_time": "'$(date -u +%Y-%m-%dT%H:%M:%SZ)'",
                "end_time": null
            }
        ]
    }'
    
    echo -e "${CYAN}发送带状态信息的分析请求...${NC}"
    local response=$(curl -s -X POST "${CABY_AI_URL}/api/v1/analyze" \
        -H "Content-Type: application/json" \
        -H "X-User-ID: test_user_001" \
        -d "$test_data" \
        -w "%{http_code}")
    
    local http_code="${response: -3}"
    local body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        print_success "集成测试请求成功"
        echo -e "${CYAN}响应摘要:${NC}"
        echo "$body" | python3 -m json.tool 2>/dev/null | head -20 || echo "$body"
        return 0
    else
        print_error "集成测试请求失败 (HTTP $http_code)"
        echo "$body"
        return 1
    fi
}

# 检查测试结果
check_results() {
    print_step "检查测试结果"
    
    if [ -f "test_results.json" ]; then
        print_success "测试结果文件存在"
        
        # 显示新功能测试结果
        echo -e "${CYAN}新功能测试结果:${NC}"
        
        if command -v jq > /dev/null 2>&1; then
            # 使用 jq 解析结果
            for test in "cat_states" "vector_evolution" "feature_protection" "shadow_mode_enhanced"; do
                local result=$(jq -r ".$test.success // false" test_results.json 2>/dev/null)
                if [ "$result" = "true" ]; then
                    echo -e "  ✅ $test"
                elif [ "$result" = "false" ]; then
                    echo -e "  ❌ $test"
                else
                    echo -e "  ⚪ $test (未测试)"
                fi
            done
        else
            print_warning "安装 jq 以获得更好的结果显示"
        fi
    else
        print_warning "未找到测试结果文件"
    fi
}

# 显示日志摘要
show_logs() {
    print_step "显示相关日志"
    
    if [ -f "log/caby_ai.log" ]; then
        echo -e "${CYAN}最近的相关日志:${NC}"
        grep -E "(cat_states|vector|evolution|protection|shadow)" log/caby_ai.log | tail -10 || true
    else
        print_warning "未找到日志文件"
    fi
}

# 主执行流程
main() {
    # 检查当前目录
    if [ ! -f "scripts/test.py" ]; then
        print_error "请在 caby_ai 根目录下运行此脚本"
        exit 1
    fi
    
    # 执行测试
    check_services
    
    local success_count=0
    local total_tests=5
    
    # 运行各项测试
    if test_cat_states; then
        ((success_count++))
    fi
    
    if test_vector_evolution; then
        ((success_count++))
    fi
    
    if test_feature_protection; then
        ((success_count++))
    fi
    
    if test_shadow_mode_enhanced; then
        ((success_count++))
    fi
    
    if run_integration_test; then
        ((success_count++))
    fi
    
    # 检查结果
    check_results
    show_logs
    
    # 显示总结
    echo -e "\n${WHITE}📊 测试总结${NC}"
    echo -e "${CYAN}通过测试: ${success_count}/${total_tests}${NC}"
    
    if [ $success_count -eq $total_tests ]; then
        print_success "所有新功能测试通过！"
        echo -e "${GREEN}🎉 可以安全提交代码到 git${NC}"
        exit 0
    elif [ $success_count -gt 0 ]; then
        print_warning "部分测试通过，请检查失败的测试"
        exit 1
    else
        print_error "所有测试失败，请检查服务状态和配置"
        exit 1
    fi
}

# 运行主函数
main "$@"
