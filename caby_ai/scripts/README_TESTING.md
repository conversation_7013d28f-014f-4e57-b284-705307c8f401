# Caby AI 测试使用说明

## 快速开始

### 1. 部署服务
```bash
# 在 caby_ai 目录下
./scripts/quick_deploy.sh
```

### 2. 运行新功能测试
```bash
# 快速测试新功能
./scripts/test_new_features.sh

# 或者使用 test.py 进行详细测试
python3 scripts/test.py cat_states vector_evolution feature_protection \
    --url http://**********:8765 \
    --backend-url http://**********:8080 \
    --verbose
```

## 测试说明

### 服务要求
- **Caby AI**: 必须运行在 http://**********:8765
- **Backend Server**: 推荐运行在 http://**********:8080 (可选)

### 测试内容

#### 1. Cat States (猫咪状态管理)
- 标记猫咪医疗状态
- 查询猫咪状态
- 带状态信息的分析

#### 2. Vector Evolution (向量演化)
- 特征存储和演化
- 相似度查找验证

#### 3. Feature Protection (特征保护)
- 医疗状态下的特征保护
- 状态结束后保护解除

#### 4. Enhanced Shadow Mode (增强影子模式)
- 状态感知的影子模式分析
- 配置获取和验证

## 常见问题

### JSON解析错误
如果遇到 "Expecting value: line 1 column 1 (char 0)" 错误：

1. **检查服务状态**
   ```bash
   curl http://**********:8765/health
   ```

2. **查看服务日志**
   ```bash
   ./scripts/logs.sh -n 50
   ```

3. **检查端口占用**
   ```bash
   netstat -tlnp | grep 8765
   ```

### Backend Server 不可用
如果 Backend Server 不可用：
- Cat States 测试会失败，但其他测试仍会继续
- 可以只测试 Caby AI 相关功能

### 测试失败处理
1. 查看详细错误信息（使用 --verbose）
2. 检查服务日志
3. 确认网络连接
4. 重新部署服务

## 测试命令参考

### 单项测试
```bash
# 健康检查
python3 scripts/test.py health --url http://**********:8765

# 猫咪状态管理
python3 scripts/test.py cat_states \
    --url http://**********:8765 \
    --backend-url http://**********:8080 \
    --verbose

# 向量演化
python3 scripts/test.py vector_evolution \
    --url http://**********:8765 \
    --verbose

# 特征保护
python3 scripts/test.py feature_protection \
    --url http://**********:8765 \
    --backend-url http://**********:8080 \
    --verbose

# 增强影子模式
python3 scripts/test.py shadow_mode_enhanced \
    --url http://**********:8765 \
    --verbose
```

### 批量测试
```bash
# 所有新功能
python3 scripts/test.py cat_states vector_evolution feature_protection shadow_mode_enhanced \
    --url http://**********:8765 \
    --backend-url http://**********:8080 \
    --verbose

# 完整测试套件
python3 scripts/test.py all \
    --url http://**********:8765 \
    --backend-url http://**********:8080 \
    --verbose
```

## 成功标准

### 测试通过条件
- 至少 3/5 的测试通过
- 无严重错误（如服务不可访问）
- 核心功能正常响应

### 预期结果
- ✅ Health Check: 服务正常运行
- ✅ Cat States: API调用成功（需要Backend Server）
- ✅ Vector Evolution: 特征存储和查找正常
- ✅ Feature Protection: 状态感知分析正常
- ✅ Enhanced Shadow Mode: 影子模式功能正常

## 注意事项

1. **不要在测试脚本中重新构建Docker**
2. **所有测试逻辑统一维护在 test.py 中**
3. **测试脚本只负责调用 test.py，不做具体测试实现**
4. **遇到JSON解析错误时，检查服务响应格式**
5. **Backend Server 不可用时，部分测试会跳过但不影响整体结果**
