#!/bin/bash

# 检查向量存储配置的脚本

echo "========================================"
echo "CABY_AI VECTOR STORAGE CONFIGURATION CHECK"
echo "========================================"

# 检查环境变量
echo "Environment Variables:"
echo "  SHADOW_MODE_ENABLED: ${SHADOW_MODE_ENABLED:-not set}"
echo "  SHADOW_STORE_FEATURES: ${SHADOW_STORE_FEATURES:-not set (default: true)}"
echo "  SHADOW_STORE_THRESHOLD: ${SHADOW_STORE_THRESHOLD:-not set (default: 0.0)}"
echo "  SHADOW_MAX_FEATURES_PER_CAT: ${SHADOW_MAX_FEATURES_PER_CAT:-not set (default: 1000)}"
echo "  QDRANT_HOST: ${QDRANT_HOST:-not set}"
echo "  QDRANT_PORT: ${QDRANT_PORT:-not set}"
echo "  QDRANT_API_KEY: ${QDRANT_API_KEY:+***set***}"

echo ""
echo "Configuration Analysis:"

# 检查影子模式是否启用
if [ "$SHADOW_MODE_ENABLED" = "true" ]; then
    echo "  ✅ Shadow mode is ENABLED"
else
    echo "  ❌ Shadow mode is DISABLED or not set"
    echo "     Set SHADOW_MODE_ENABLED=true to enable vector storage"
fi

# 检查特征存储是否启用
if [ "$SHADOW_STORE_FEATURES" = "false" ]; then
    echo "  ❌ Feature storage is DISABLED"
    echo "     Set SHADOW_STORE_FEATURES=true to enable vector storage"
else
    echo "  ✅ Feature storage is ENABLED (default or explicitly set)"
fi

# 检查Qdrant配置
if [ -n "$QDRANT_HOST" ] && [ -n "$QDRANT_PORT" ]; then
    echo "  ✅ Qdrant connection configured: ${QDRANT_HOST}:${QDRANT_PORT}"
else
    echo "  ❌ Qdrant connection not properly configured"
    echo "     Set QDRANT_HOST and QDRANT_PORT environment variables"
fi

echo ""
echo "Optimized Configuration for Vector Storage:"
echo "  # 基础配置"
echo "  export SHADOW_MODE_ENABLED=true"
echo "  export SHADOW_STORE_FEATURES=true"
echo "  export SHADOW_STORE_THRESHOLD=0.3  # 优化：过滤低质量特征"
echo "  export SHADOW_MAX_FEATURES_PER_CAT=300  # 优化：平衡性能和效果"
echo ""
echo "  # 演化机制配置"
echo "  export SHADOW_EVOLUTION_STRATEGY=hybrid"
echo "  export SHADOW_EVOLUTION_CYCLE=168  # 每周演化一次"
echo "  export SHADOW_TIME_DECAY_FACTOR=30.0  # 30天衰减因子"
echo "  export SHADOW_QUALITY_WEIGHT_RATIO=0.7  # 质量权重70%"
echo ""
echo "  # Qdrant连接配置"
echo "  export QDRANT_HOST=your_qdrant_host"
echo "  export QDRANT_PORT=6333"
echo "  export QDRANT_API_KEY=your_api_key"

echo ""
echo "========================================"
