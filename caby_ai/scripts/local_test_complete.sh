#!/bin/bash

# 本地完整测试脚本 - 包括cat states和vector演化功能
# 使用本地IP: **********
# 作者: Caby AI Team
# 日期: 2025-01-18

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# 配置
LOCAL_IP="**********"
CABY_AI_PORT="8081"
BACKEND_PORT="8080"
CABY_AI_URL="http://${LOCAL_IP}:${CABY_AI_PORT}"
BACKEND_URL="http://${LOCAL_IP}:${BACKEND_PORT}"

echo -e "${WHITE}🚀 Caby AI 本地完整测试脚本${NC}"
echo -e "${CYAN}本地IP: ${LOCAL_IP}${NC}"
echo -e "${CYAN}Caby AI URL: ${CABY_AI_URL}${NC}"
echo -e "${CYAN}Backend URL: ${BACKEND_URL}${NC}"
echo "=" * 60

# 函数：打印步骤
print_step() {
    echo -e "\n${BLUE}📋 步骤 $1: $2${NC}"
}

# 函数：打印成功
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# 函数：打印错误
print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 函数：打印警告
print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# 函数：检查服务状态
check_service() {
    local url=$1
    local name=$2
    
    echo -e "${CYAN}检查 ${name} 服务状态...${NC}"
    
    if curl -s --connect-timeout 5 "${url}/health" > /dev/null 2>&1; then
        print_success "${name} 服务运行正常"
        return 0
    else
        print_error "${name} 服务未运行或不可访问"
        return 1
    fi
}

# 函数：等待服务启动
wait_for_service() {
    local url=$1
    local name=$2
    local max_wait=60
    local count=0
    
    echo -e "${CYAN}等待 ${name} 服务启动...${NC}"
    
    while [ $count -lt $max_wait ]; do
        if curl -s --connect-timeout 2 "${url}/health" > /dev/null 2>&1; then
            print_success "${name} 服务已启动"
            return 0
        fi
        
        echo -n "."
        sleep 2
        count=$((count + 2))
    done
    
    print_error "${name} 服务启动超时"
    return 1
}

# 主要步骤

print_step "1" "检查当前目录"
if [ ! -f "scripts/quick_deploy.sh" ]; then
    print_error "请在 caby_ai 根目录下运行此脚本"
    exit 1
fi
print_success "当前目录正确"

print_step "2" "检查依赖服务"
echo -e "${CYAN}检查 Backend Server...${NC}"
if ! check_service "${BACKEND_URL}" "Backend Server"; then
    print_warning "Backend Server 未运行，某些测试可能失败"
    print_warning "请确保 backend_server 已启动并运行在 ${BACKEND_URL}"
fi

print_step "3" "构建和部署 Caby AI"
echo -e "${CYAN}执行 quick_deploy.sh...${NC}"
if ./scripts/quick_deploy.sh; then
    print_success "Caby AI 部署成功"
else
    print_error "Caby AI 部署失败"
    exit 1
fi

print_step "4" "等待服务启动"
if wait_for_service "${CABY_AI_URL}" "Caby AI"; then
    print_success "Caby AI 服务已就绪"
else
    print_error "Caby AI 服务启动失败"
    exit 1
fi

print_step "5" "运行基础健康检查"
echo -e "${CYAN}运行基础测试...${NC}"
python3 scripts/test.py health vision_health \
    --url "${CABY_AI_URL}" \
    --backend-url "${BACKEND_URL}" \
    --verbose

if [ $? -eq 0 ]; then
    print_success "基础健康检查通过"
else
    print_error "基础健康检查失败"
    exit 1
fi

print_step "6" "测试猫咪状态管理功能"
echo -e "${CYAN}测试 cat states 功能...${NC}"
python3 scripts/test.py cat_states \
    --url "${CABY_AI_URL}" \
    --backend-url "${BACKEND_URL}" \
    --verbose

print_step "7" "测试向量演化功能"
echo -e "${CYAN}测试 vector evolution 功能...${NC}"
python3 scripts/test.py vector_evolution \
    --url "${CABY_AI_URL}" \
    --backend-url "${BACKEND_URL}" \
    --verbose

print_step "8" "测试特征保护机制"
echo -e "${CYAN}测试 feature protection 功能...${NC}"
python3 scripts/test.py feature_protection \
    --url "${CABY_AI_URL}" \
    --backend-url "${BACKEND_URL}" \
    --verbose

print_step "9" "测试增强影子模式"
echo -e "${CYAN}测试 enhanced shadow mode 功能...${NC}"
python3 scripts/test.py shadow_mode_enhanced \
    --url "${CABY_AI_URL}" \
    --backend-url "${BACKEND_URL}" \
    --verbose

print_step "10" "运行完整测试套件"
echo -e "${CYAN}运行所有测试...${NC}"
python3 scripts/test.py all \
    --url "${CABY_AI_URL}" \
    --backend-url "${BACKEND_URL}" \
    --verbose

print_step "11" "检查测试结果"
if [ -f "test_results.json" ]; then
    print_success "测试结果已保存到 test_results.json"
    echo -e "${CYAN}测试结果摘要:${NC}"
    
    # 使用 jq 解析结果（如果可用）
    if command -v jq > /dev/null 2>&1; then
        echo -e "${CYAN}成功的测试:${NC}"
        jq -r 'to_entries[] | select(.value.success == true) | "  ✅ " + .key' test_results.json 2>/dev/null || true
        
        echo -e "${CYAN}失败的测试:${NC}"
        jq -r 'to_entries[] | select(.value.success == false) | "  ❌ " + .key + ": " + .value.error' test_results.json 2>/dev/null || true
    else
        print_warning "安装 jq 以获得更好的结果显示"
        cat test_results.json
    fi
else
    print_warning "未找到测试结果文件"
fi

print_step "12" "查看服务日志"
echo -e "${CYAN}最近的 Caby AI 日志:${NC}"
if [ -f "log/caby_ai.log" ]; then
    tail -20 log/caby_ai.log
else
    print_warning "未找到日志文件"
fi

echo -e "\n${WHITE}🎉 本地测试完成！${NC}"
echo -e "${CYAN}测试包括:${NC}"
echo -e "  • 基础健康检查"
echo -e "  • 猫咪状态管理 (Cat States)"
echo -e "  • 向量演化功能 (Vector Evolution)"
echo -e "  • 特征保护机制 (Feature Protection)"
echo -e "  • 增强影子模式 (Enhanced Shadow Mode)"
echo -e "  • 完整功能测试套件"

echo -e "\n${CYAN}服务访问地址:${NC}"
echo -e "  • Caby AI: ${CABY_AI_URL}"
echo -e "  • Backend Server: ${BACKEND_URL}"

echo -e "\n${CYAN}下一步建议:${NC}"
echo -e "  1. 检查 test_results.json 了解详细结果"
echo -e "  2. 查看 log/caby_ai.log 了解服务运行状态"
echo -e "  3. 如有问题，使用 scripts/logs.sh 查看实时日志"
echo -e "  4. 测试通过后可以提交代码到 git"

echo -e "\n${GREEN}测试脚本执行完成！${NC}"
