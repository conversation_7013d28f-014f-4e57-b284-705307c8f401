# config/config.yaml
server:
  listen_addr: ":8765"
  max_concurrency: 2  # Maximum number of segments to process concurrently (controls CPU usage)

auth:
  # This token should match the one used by backend_server when calling caby_ai
  service_token: "${CABY_AI_SERVICE_TOKEN}"

qdrant:
  host: "${QDRANT_HOST}:${QDRANT_PORT}" # Qdrant service address with port
  scheme: "${QDRANT_SCHEME}" # http or https
  api_key: "${QDRANT_API_KEY}" # API key for Qdrant authentication

vision:
  host: "${VISION_HOST}" # Docker service name or IP
  port: 8001
  api_key: "${VISION_API_KEY}" # Vision API密钥，通过环境变量设置
  timeout: 30 # Request timeout in seconds

shadow_mode:
  enabled: ${SHADOW_MODE_ENABLED}
  similarity_threshold: ${SHADOW_SIMILARITY_THRESHOLD}
  new_cat_threshold: ${SHADOW_NEW_CAT_THRESHOLD}
  top_k: ${SHADOW_TOP_K}
  # 向量存储配置
  store_features: ${SHADOW_STORE_FEATURES:true}  # 是否存储特征向量用于学习
  store_threshold: ${SHADOW_STORE_THRESHOLD:0.3}  # 只存储相似度高于此阈值的特征
  max_features_per_cat: ${SHADOW_MAX_FEATURES_PER_CAT:300}  # 每只猫最多存储的特征数量（优化值）
  # 演化机制配置
  evolution_strategy: ${SHADOW_EVOLUTION_STRATEGY:hybrid}  # 演化策略: time_based, quality_based, hybrid
  evolution_cycle: ${SHADOW_EVOLUTION_CYCLE:336}  # 演化周期（小时）- 立即调整为2周
  time_decay_factor: ${SHADOW_TIME_DECAY_FACTOR:30.0}  # 时间衰减因子（天）
  quality_weight_ratio: ${SHADOW_QUALITY_WEIGHT_RATIO:0.7}  # 质量权重比例
  # 特征保护配置
  feature_protection: ${SHADOW_FEATURE_PROTECTION:true}  # 启用特征保护
  recent_days_protection: ${SHADOW_RECENT_DAYS_PROTECTION:7}  # 保护最近N天的特征
  protection_percentage: ${SHADOW_PROTECTION_PERCENTAGE:20}  # 保护百分比

backend_server_url: "https://api.caby.care" # URL of the backend_server
