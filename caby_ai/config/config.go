package config

import (
	"log"
	"os"
	"regexp"
	"strconv"

	"gopkg.in/yaml.v3"
)

type Config struct {
	Server              ServerConfig     `yaml:"server"`
	Auth                AuthConfig       `yaml:"auth"`
	Qdrant              QdrantConfig     `yaml:"qdrant"`
	Vision              VisionConfig     `yaml:"vision"`
	ShadowMode          ShadowModeConfig `yaml:"shadow_mode"`
	BackendServerURL    string           `yaml:"backend_server_url"`
	BackendServiceToken string           `yaml:"backend_service_token"`
}

type ServerConfig struct {
	ListenAddr     string `yaml:"listen_addr"`
	MaxConcurrency int    `yaml:"max_concurrency"` // Maximum number of concurrent segments to process
}

type AuthConfig struct {
	ServiceToken string `yaml:"service_token"` // Token for internal service communication
}

type QdrantConfig struct {
	Host   string `yaml:"host"`
	Scheme string `yaml:"scheme"`
	ApiKey string `yaml:"api_key"` // API key for authentication
}

type VisionConfig struct {
	Host    string `yaml:"host"`
	Port    int    `yaml:"port"`
	ApiKey  string `yaml:"api_key"`
	Timeout int    `yaml:"timeout"` // Request timeout in seconds
}

type ShadowModeConfig struct {
	Enabled             bool    `yaml:"enabled" json:"enabled"`
	SimilarityThreshold float64 `yaml:"similarity_threshold" json:"similarity_threshold"`
	NewCatThreshold     float64 `yaml:"new_cat_threshold" json:"new_cat_threshold"`
	TopK                int     `yaml:"top_k" json:"top_k"`
}

// expandEnvVars replaces ${VAR} with environment variable values
func expandEnvVars(data []byte) []byte {
	// Regular expression to match ${VAR} patterns
	re := regexp.MustCompile(`\$\{([^}]+)\}`)

	result := re.ReplaceAllFunc(data, func(match []byte) []byte {
		// Extract variable name from ${VAR}
		varName := string(match[2 : len(match)-1]) // Remove ${ and }

		// Get environment variable value
		envValue := os.Getenv(varName)
		// Always replace with the environment value (empty string if not set)
		return []byte(envValue)
	})

	return result
}

// LoadConfig loads configuration from a YAML file.
func LoadConfig(path string) (*Config, error) {
	data, err := os.ReadFile(path)
	if err != nil {
		return nil, err
	}

	// Expand environment variables in the YAML content
	expandedData := expandEnvVars(data)

	var cfg Config
	err = yaml.Unmarshal(expandedData, &cfg)
	if err != nil {
		return nil, err
	}

	// Additional environment variable overrides
	if qdrantKey := os.Getenv("QDRANT_API_KEY"); qdrantKey != "" {
		cfg.Qdrant.ApiKey = qdrantKey
		log.Printf("QDRANT_API_KEY: %s", qdrantKey)
	}

	// Set default scheme if not provided
	if cfg.Qdrant.Scheme == "" {
		cfg.Qdrant.Scheme = "http"
	}

	// Override scheme from environment variable
	if qdrantScheme := os.Getenv("QDRANT_SCHEME"); qdrantScheme != "" {
		cfg.Qdrant.Scheme = qdrantScheme
		log.Printf("QDRANT_SCHEME: %s", qdrantScheme)
	}

	if visionKey := os.Getenv("VISION_API_KEY"); visionKey != "" {
		cfg.Vision.ApiKey = visionKey
		log.Printf("VISION_API_KEY: %s", visionKey)
	}

	if visionHost := os.Getenv("VISION_HOST"); visionHost != "" {
		cfg.Vision.Host = visionHost
		log.Printf("VISION_HOST: %s", visionHost)
	}

	if serviceToken := os.Getenv("CABY_AI_SERVICE_TOKEN"); serviceToken != "" {
		cfg.Auth.ServiceToken = serviceToken
		log.Printf("CABY_AI_SERVICE_TOKEN: %s", serviceToken)
	}

	// Shadow mode environment variable overrides
	if shadowEnabled := os.Getenv("SHADOW_MODE_ENABLED"); shadowEnabled != "" {
		if shadowEnabled == "true" {
			cfg.ShadowMode.Enabled = true
		} else {
			cfg.ShadowMode.Enabled = false
		}
		log.Printf("SHADOW_MODE_ENABLED: %v", cfg.ShadowMode.Enabled)
	}

	if similarityThreshold := os.Getenv("SHADOW_SIMILARITY_THRESHOLD"); similarityThreshold != "" {
		if val, err := strconv.ParseFloat(similarityThreshold, 64); err == nil {
			cfg.ShadowMode.SimilarityThreshold = val
			log.Printf("SHADOW_SIMILARITY_THRESHOLD: %f", val)
		}
	}

	if newCatThreshold := os.Getenv("SHADOW_NEW_CAT_THRESHOLD"); newCatThreshold != "" {
		if val, err := strconv.ParseFloat(newCatThreshold, 64); err == nil {
			cfg.ShadowMode.NewCatThreshold = val
			log.Printf("SHADOW_NEW_CAT_THRESHOLD: %f", val)
		}
	}

	if topK := os.Getenv("SHADOW_TOP_K"); topK != "" {
		if val, err := strconv.Atoi(topK); err == nil {
			cfg.ShadowMode.TopK = val
			log.Printf("SHADOW_TOP_K: %d", val)
		}
	}

	return &cfg, nil
}
