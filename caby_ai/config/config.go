package config

import (
	"log"
	"os"
	"regexp"
	"strconv"

	"gopkg.in/yaml.v3"
)

type Config struct {
	Server              ServerConfig     `yaml:"server"`
	Auth                AuthConfig       `yaml:"auth"`
	Qdrant              QdrantConfig     `yaml:"qdrant"`
	Vision              VisionConfig     `yaml:"vision"`
	ShadowMode          ShadowModeConfig `yaml:"shadow_mode"`
	BackendServerURL    string           `yaml:"backend_server_url"`
	BackendServiceToken string           `yaml:"backend_service_token"`
}

type ServerConfig struct {
	ListenAddr     string `yaml:"listen_addr"`
	MaxConcurrency int    `yaml:"max_concurrency"` // Maximum number of concurrent segments to process
}

type AuthConfig struct {
	ServiceToken string `yaml:"service_token"` // Token for internal service communication
}

type QdrantConfig struct {
	Host   string `yaml:"host"`
	Scheme string `yaml:"scheme"`
	ApiKey string `yaml:"api_key"` // API key for authentication
}

type VisionConfig struct {
	Host    string `yaml:"host"`
	Port    int    `yaml:"port"`
	ApiKey  string `yaml:"api_key"`
	Timeout int    `yaml:"timeout"` // Request timeout in seconds
}

type ShadowModeConfig struct {
	Enabled             bool    `yaml:"enabled" json:"enabled"`
	SimilarityThreshold float64 `yaml:"similarity_threshold" json:"similarity_threshold"`
	NewCatThreshold     float64 `yaml:"new_cat_threshold" json:"new_cat_threshold"`
	TopK                int     `yaml:"top_k" json:"top_k"`
	// 向量存储配置
	StoreFeatures     bool    `yaml:"store_features" json:"store_features"`
	StoreThreshold    float64 `yaml:"store_threshold" json:"store_threshold"`
	MaxFeaturesPerCat int     `yaml:"max_features_per_cat" json:"max_features_per_cat"`
	// 演化机制配置
	EvolutionStrategy  string  `yaml:"evolution_strategy" json:"evolution_strategy"`
	EvolutionCycle     int     `yaml:"evolution_cycle" json:"evolution_cycle"`
	TimeDecayFactor    float64 `yaml:"time_decay_factor" json:"time_decay_factor"`
	QualityWeightRatio float64 `yaml:"quality_weight_ratio" json:"quality_weight_ratio"`
}

// expandEnvVars replaces ${VAR} with environment variable values
func expandEnvVars(data []byte) []byte {
	// Regular expression to match ${VAR} patterns
	re := regexp.MustCompile(`\$\{([^}]+)\}`)

	result := re.ReplaceAllFunc(data, func(match []byte) []byte {
		// Extract variable name from ${VAR}
		varName := string(match[2 : len(match)-1]) // Remove ${ and }

		// Get environment variable value
		envValue := os.Getenv(varName)
		// Always replace with the environment value (empty string if not set)
		return []byte(envValue)
	})

	return result
}

// LoadConfig loads configuration from a YAML file.
func LoadConfig(path string) (*Config, error) {
	data, err := os.ReadFile(path)
	if err != nil {
		return nil, err
	}

	// Expand environment variables in the YAML content
	expandedData := expandEnvVars(data)

	var cfg Config
	err = yaml.Unmarshal(expandedData, &cfg)
	if err != nil {
		return nil, err
	}

	// Additional environment variable overrides
	if qdrantKey := os.Getenv("QDRANT_API_KEY"); qdrantKey != "" {
		cfg.Qdrant.ApiKey = qdrantKey
		log.Printf("QDRANT_API_KEY: %s", qdrantKey)
	}

	// Set default scheme if not provided
	if cfg.Qdrant.Scheme == "" {
		cfg.Qdrant.Scheme = "http"
	}

	// Override scheme from environment variable
	if qdrantScheme := os.Getenv("QDRANT_SCHEME"); qdrantScheme != "" {
		cfg.Qdrant.Scheme = qdrantScheme
		log.Printf("QDRANT_SCHEME: %s", qdrantScheme)
	}

	if visionKey := os.Getenv("VISION_API_KEY"); visionKey != "" {
		cfg.Vision.ApiKey = visionKey
		log.Printf("VISION_API_KEY: %s", visionKey)
	}

	if visionHost := os.Getenv("VISION_HOST"); visionHost != "" {
		cfg.Vision.Host = visionHost
		log.Printf("VISION_HOST: %s", visionHost)
	}

	if serviceToken := os.Getenv("CABY_AI_SERVICE_TOKEN"); serviceToken != "" {
		cfg.Auth.ServiceToken = serviceToken
		log.Printf("CABY_AI_SERVICE_TOKEN: %s", serviceToken)
	}

	// Shadow mode environment variable overrides
	if shadowEnabled := os.Getenv("SHADOW_MODE_ENABLED"); shadowEnabled != "" {
		if shadowEnabled == "true" {
			cfg.ShadowMode.Enabled = true
		} else {
			cfg.ShadowMode.Enabled = false
		}
		log.Printf("SHADOW_MODE_ENABLED: %v", cfg.ShadowMode.Enabled)
	}

	if similarityThreshold := os.Getenv("SHADOW_SIMILARITY_THRESHOLD"); similarityThreshold != "" {
		if val, err := strconv.ParseFloat(similarityThreshold, 64); err == nil {
			cfg.ShadowMode.SimilarityThreshold = val
			log.Printf("SHADOW_SIMILARITY_THRESHOLD: %f", val)
		}
	}

	if newCatThreshold := os.Getenv("SHADOW_NEW_CAT_THRESHOLD"); newCatThreshold != "" {
		if val, err := strconv.ParseFloat(newCatThreshold, 64); err == nil {
			cfg.ShadowMode.NewCatThreshold = val
			log.Printf("SHADOW_NEW_CAT_THRESHOLD: %f", val)
		}
	}

	if topK := os.Getenv("SHADOW_TOP_K"); topK != "" {
		if val, err := strconv.Atoi(topK); err == nil {
			cfg.ShadowMode.TopK = val
			log.Printf("SHADOW_TOP_K: %d", val)
		}
	}

	// 向量存储配置的环境变量覆盖
	if storeFeatures := os.Getenv("SHADOW_STORE_FEATURES"); storeFeatures != "" {
		if storeFeatures == "true" {
			cfg.ShadowMode.StoreFeatures = true
		} else {
			cfg.ShadowMode.StoreFeatures = false
		}
		log.Printf("SHADOW_STORE_FEATURES: %v", cfg.ShadowMode.StoreFeatures)
	} else {
		// 默认启用特征存储
		cfg.ShadowMode.StoreFeatures = true
	}

	if storeThreshold := os.Getenv("SHADOW_STORE_THRESHOLD"); storeThreshold != "" {
		if val, err := strconv.ParseFloat(storeThreshold, 64); err == nil {
			cfg.ShadowMode.StoreThreshold = val
			log.Printf("SHADOW_STORE_THRESHOLD: %f", val)
		}
	}

	if maxFeatures := os.Getenv("SHADOW_MAX_FEATURES_PER_CAT"); maxFeatures != "" {
		if val, err := strconv.Atoi(maxFeatures); err == nil {
			cfg.ShadowMode.MaxFeaturesPerCat = val
			log.Printf("SHADOW_MAX_FEATURES_PER_CAT: %d", val)
		}
	} else {
		// 设置默认值为300（优化后的推荐值）
		cfg.ShadowMode.MaxFeaturesPerCat = 300
	}

	// 演化机制配置的环境变量覆盖
	if evolutionStrategy := os.Getenv("SHADOW_EVOLUTION_STRATEGY"); evolutionStrategy != "" {
		cfg.ShadowMode.EvolutionStrategy = evolutionStrategy
		log.Printf("SHADOW_EVOLUTION_STRATEGY: %s", evolutionStrategy)
	} else {
		cfg.ShadowMode.EvolutionStrategy = "hybrid"
	}

	if evolutionCycle := os.Getenv("SHADOW_EVOLUTION_CYCLE"); evolutionCycle != "" {
		if val, err := strconv.Atoi(evolutionCycle); err == nil {
			cfg.ShadowMode.EvolutionCycle = val
			log.Printf("SHADOW_EVOLUTION_CYCLE: %d", val)
		}
	} else {
		cfg.ShadowMode.EvolutionCycle = 168 // 默认每周清理一次
	}

	// 时间衰减因子（天）
	if timeDecay := os.Getenv("SHADOW_TIME_DECAY_FACTOR"); timeDecay != "" {
		if val, err := strconv.ParseFloat(timeDecay, 64); err == nil {
			cfg.ShadowMode.TimeDecayFactor = val
			log.Printf("SHADOW_TIME_DECAY_FACTOR: %f", val)
		}
	} else {
		cfg.ShadowMode.TimeDecayFactor = 30.0 // 30天衰减因子
	}

	// 质量权重比例
	if qualityWeight := os.Getenv("SHADOW_QUALITY_WEIGHT_RATIO"); qualityWeight != "" {
		if val, err := strconv.ParseFloat(qualityWeight, 64); err == nil {
			cfg.ShadowMode.QualityWeightRatio = val
			log.Printf("SHADOW_QUALITY_WEIGHT_RATIO: %f", val)
		}
	} else {
		cfg.ShadowMode.QualityWeightRatio = 0.7 // 质量权重占70%
	}

	return &cfg, nil
}
