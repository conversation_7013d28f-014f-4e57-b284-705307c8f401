# Caby AI 本地测试指南

## 概述

本指南帮助你在本地环境中测试 Caby AI 的新功能，包括：
- 猫咪状态管理 (Cat States)
- 向量演化功能 (Vector Evolution)
- 特征保护机制 (Feature Protection)
- 增强影子模式 (Enhanced Shadow Mode)

## 环境配置

### 基本信息
- **本地IP**: **********
- **Caby AI端口**: 8081
- **Backend Server端口**: 8080
- **Caby AI URL**: http://**********:8081
- **Backend URL**: http://**********:8080

### 前置条件
1. **Backend Server 运行中**
   ```bash
   # 确保 backend_server 已启动
   curl http://**********:8080/health
   ```

2. **Python 依赖**
   ```bash
   pip install requests pillow
   ```

3. **可选工具**
   ```bash
   # 用于更好的JSON显示
   sudo apt install jq  # Ubuntu/Debian
   brew install jq      # macOS
   ```

## 快速开始

### 1. 部署和基础测试
```bash
# 在 caby_ai 目录下
cd /path/to/caby_ai

# 部署服务
./scripts/quick_deploy.sh

# 等待服务启动，然后运行基础测试
python3 scripts/test.py health vision_health \
    --url http://**********:8081 \
    --backend-url http://**********:8080 \
    --verbose
```

### 2. 测试新功能（推荐）
```bash
# 运行新功能快速测试
./scripts/test_new_features.sh
```

### 3. 完整测试套件
```bash
# 运行完整测试（包括所有功能）
./scripts/local_test_complete.sh
```

## 分项测试

### 猫咪状态管理测试
```bash
python3 scripts/test.py cat_states \
    --url http://**********:8081 \
    --backend-url http://**********:8080 \
    --verbose
```

**测试内容**:
- 标记猫咪医疗状态
- 查询猫咪状态
- 带状态信息的分析请求

### 向量演化功能测试
```bash
python3 scripts/test.py vector_evolution \
    --url http://**********:8081 \
    --backend-url http://**********:8080 \
    --verbose
```

**测试内容**:
- 多次特征存储
- 演化触发机制
- 相似度查找验证

### 特征保护机制测试
```bash
python3 scripts/test.py feature_protection \
    --url http://**********:8081 \
    --backend-url http://**********:8080 \
    --verbose
```

**测试内容**:
- 医疗状态标记
- 保护状态下的分析
- 状态结束和保护解除

### 增强影子模式测试
```bash
python3 scripts/test.py shadow_mode_enhanced \
    --url http://**********:8081 \
    --backend-url http://**********:8080 \
    --verbose
```

**测试内容**:
- 影子模式配置获取
- 状态感知的分析
- 影子模式结果验证

## 手动测试

### 1. 健康检查
```bash
# Caby AI 健康检查
curl http://**********:8081/health

# Backend Server 健康检查
curl http://**********:8080/health
```

### 2. 猫咪状态API测试
```bash
# 标记猫咪状态
curl -X POST http://**********:8080/api/cats/test_cat_001/state \
  -H "X-User-ID: test_user_001" \
  -H "Content-Type: application/json" \
  -d '{
    "state": "medical",
    "description": "Manual test",
    "expected_duration_days": 7
  }'

# 查询猫咪状态
curl http://**********:8080/api/cats/test_cat_001/state \
  -H "X-User-ID: test_user_001"

# 结束猫咪状态
curl -X DELETE http://**********:8080/api/cats/test_cat_001/state \
  -H "X-User-ID: test_user_001"
```

### 3. 分析API测试
```bash
# 带状态信息的分析
curl -X POST http://**********:8081/api/v1/analyze \
  -H "X-User-ID: test_user_001" \
  -H "Content-Type: application/json" \
  -d '{
    "video_id": "manual_test_'$(date +%s)'",
    "user_id": "test_user_001",
    "cat_states": [
      {
        "cat_id": "test_cat_001",
        "state": "medical",
        "description": "Manual test",
        "start_time": "'$(date -u +%Y-%m-%dT%H:%M:%SZ)'",
        "end_time": null
      }
    ]
  }'
```

## 结果检查

### 1. 测试结果文件
```bash
# 查看测试结果
cat test_results.json

# 使用 jq 格式化显示
jq . test_results.json
```

### 2. 日志检查
```bash
# 查看实时日志
./scripts/logs.sh -f

# 查看最近日志
./scripts/logs.sh -n 100

# 搜索特定关键词
grep -E "(cat_states|vector|evolution|protection)" log/caby_ai.log
```

### 3. 服务状态
```bash
# 检查容器状态
docker ps | grep caby

# 检查资源使用
docker stats caby_ai_container
```

## 故障排除

### 常见问题

1. **Backend Server 不可访问**
   ```bash
   # 检查 backend_server 是否运行
   curl http://**********:8080/health
   
   # 如果未运行，启动 backend_server
   cd ../backend_server
   go run main.go
   ```

2. **Caby AI 服务启动失败**
   ```bash
   # 查看部署日志
   ./scripts/logs.sh -n 50
   
   # 重新部署
   ./scripts/quick_deploy.sh
   ```

3. **测试失败**
   ```bash
   # 检查详细错误
   python3 scripts/test.py cat_states --verbose
   
   # 检查网络连接
   curl -v http://**********:8081/health
   ```

### 调试技巧

1. **启用详细日志**
   ```bash
   # 所有测试都加上 --verbose 参数
   python3 scripts/test.py all --verbose
   ```

2. **单步测试**
   ```bash
   # 逐个运行测试，便于定位问题
   python3 scripts/test.py health --verbose
   python3 scripts/test.py cat_states --verbose
   python3 scripts/test.py vector_evolution --verbose
   ```

3. **检查配置**
   ```bash
   # 查看配置文件
   cat config/config.yaml
   
   # 检查环境变量
   env | grep -E "(CABY|BACKEND)"
   ```

## 成功标准

### 测试通过标准
- ✅ 所有健康检查通过
- ✅ Cat States API 调用成功
- ✅ Vector Evolution 功能正常
- ✅ Feature Protection 机制有效
- ✅ Enhanced Shadow Mode 运行正常

### 性能指标
- 响应时间 < 30秒
- 内存使用 < 2GB
- CPU使用率 < 80%

### 日志检查
- 无 ERROR 级别日志
- 功能相关的 INFO 日志正常
- 状态变更日志完整

## 下一步

测试通过后：
1. 检查 `test_results.json` 确认所有测试通过
2. 查看日志确认无错误
3. 提交代码到 git
4. 更新相关文档

---

**注意**: 这些功能还未提交到 git，请在本地充分测试后再提交。
