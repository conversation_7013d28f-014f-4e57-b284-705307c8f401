# 使用真实图片初始化猫咪特征向量

本文档介绍如何使用真实的猫咪图片来初始化特征向量，而不是使用生成的测试图片。

## 🎯 使用场景

- 你有真实的猫咪照片，想要生成更准确的特征向量
- 需要为新的猫咪添加特征向量
- 想要替换现有的测试数据

## 📁 准备工作

### 1. 准备猫咪图片

将你的猫咪图片放在一个目录中，例如：
```
/home/<USER>/cat_images/
├── xiaohei.jpg     # 小黑的照片
├── xiaobai.png     # 小白的照片
└── xiaohua.jpeg    # 小花的照片
```

**图片要求：**
- 支持格式：JPG, JPEG, PNG
- 建议分辨率：不超过1024x1024（脚本会自动调整）
- 图片应该清晰显示猫咪的特征

### 2. 获取猫咪ID

猫咪ID应该是唯一的标识符，格式通常为：`f3ce1b02b2c1d755421000`

## 🚀 使用方法

### 方法1：批量初始化（推荐）

#### 步骤1：创建配置文件

创建一个JSON配置文件，例如 `my_cats.json`：

```json
[
  {
    "cat_id": "f3ce1b02b2c1d755421000",
    "name": "小黑",
    "image_path": "/home/<USER>/cat_images/xiaohei.jpg"
  },
  {
    "cat_id": "f3ce1b02b40e9477c21000", 
    "name": "小白",
    "image_path": "/home/<USER>/cat_images/xiaobai.png"
  },
  {
    "cat_id": "f3ce1b02b40ed223821000",
    "name": "小花", 
    "image_path": "/home/<USER>/cat_images/xiaohua.jpeg"
  }
]
```

#### 步骤2：运行批量初始化

```bash
cd /home/<USER>/animsi/aby/server/caby_ai

python scripts/init_cats_with_images.py \
  --config my_cats.json \
  --user-id test_user_001 \
  --api-key 03U66tGbSQtHGrh9IyBDjRYaSeQukFga
```

### 方法2：单只猫咪初始化

如果你只想初始化一只猫咪：

```bash
python scripts/init_single_cat.py \
  --cat-id f3ce1b02b2c1d755421000 \
  --name 小黑 \
  --image /home/<USER>/cat_images/xiaohei.jpg \
  --user-id test_user_001 \
  --api-key 03U66tGbSQtHGrh9IyBDjRYaSeQukFga
```

## 🔧 完整工作流程

### 1. 清理现有数据（可选）

如果需要重新开始：

```bash
python scripts/cleanup_test_data.py
```

### 2. 初始化猫咪特征

使用上述方法之一初始化猫咪特征。

### 3. 验证初始化结果

```bash
python scripts/test.py test_similarity --api-key 03U66tGbSQtHGrh9IyBDjRYaSeQukFga
```

### 4. 运行完整测试

```bash
python test_shadow_complete.py
```

## 📋 示例输出

成功的初始化输出应该类似：

```
🐱 使用真实图片初始化猫咪特征向量
============================================================
用户ID: test_user_001
猫咪数量: 3

1. 处理 小黑 (ID: f3ce1b02b2c1d755421000)
   图片路径: /home/<USER>/cat_images/xiaohei.jpg
   ✅ 图片转换成功 (大小: 123456 字符)

2. 处理 小白 (ID: f3ce1b02b40e9477c21000)
   图片路径: /home/<USER>/cat_images/xiaobai.png
   ✅ 图片转换成功 (大小: 98765 字符)

3. 处理 小花 (ID: f3ce1b02b40ed223821000)
   图片路径: /home/<USER>/cat_images/xiaohua.jpeg
   ✅ 图片转换成功 (大小: 111222 字符)

📤 发送初始化请求...
✅ 猫咪特征初始化成功!

📋 初始化结果:
   ✅ 小黑 (ID: f3ce1b02b2c1d755421000)
   ✅ 小白 (ID: f3ce1b02b40e9477c21000)
   ✅ 小花 (ID: f3ce1b02b40ed223821000)

🎉 初始化完成!
```

## ⚠️ 注意事项

1. **图片质量**：使用清晰、光线良好的猫咪照片会得到更好的特征向量
2. **处理时间**：真实图片的处理时间比测试图片长，请耐心等待
3. **存储空间**：确保有足够的磁盘空间存储特征向量
4. **网络连接**：确保caby_ai和caby_vision服务正常运行

## 🔍 故障排除

### 常见错误

1. **图片文件不存在**
   - 检查图片路径是否正确
   - 确保文件权限允许读取

2. **图片格式不支持**
   - 确保使用JPG、JPEG或PNG格式
   - 可以使用图片编辑软件转换格式

3. **API请求失败**
   - 检查服务是否正常运行：`docker ps`
   - 验证API密钥是否正确
   - 查看服务日志：`docker logs caby_ai_service`

4. **特征提取失败**
   - 检查caby_vision服务状态
   - 确保图片内容清晰可识别

### 调试命令

```bash
# 检查服务状态
docker ps

# 查看服务日志
docker logs caby_ai_service --tail 50
docker logs caby_vision_service --tail 50

# 测试API连接
curl -H "Authorization: Bearer 03U66tGbSQtHGrh9IyBDjRYaSeQukFga" \
     http://localhost:8765/health
```

## 📞 获取帮助

如果遇到问题，可以：

1. 查看服务日志获取详细错误信息
2. 使用 `--verbose` 参数获取更多调试信息
3. 检查图片文件和配置文件格式
