# 向量存储机制说明

## 概述

caby_ai系统具备自动向量存储机制，能够将每次猫咪识别产生的新向量结果录入Qdrant数据库，使系统能够随着猫咪的演变而适应。

## 工作原理

### 1. 自动向量存储流程

```
用户上传图片 → caby_vision提取特征向量 → 影子模式分析 → 存储向量到Qdrant
```

每次进行影子模式识别时，系统会：

1. **提取特征向量**：调用caby_vision服务提取图片的特征向量
2. **相似度分析**：与Qdrant中已有的向量进行相似度比较
3. **存储新向量**：将新的特征向量连同元数据存储到Qdrant数据库

### 2. 存储的数据结构

每个存储的向量包含以下信息：

```json
{
  "vector": [0.1, 0.2, ...],  // 特征向量
  "payload": {
    "user_id": "用户ID",
    "cat_id": "猫咪ID",
    "video_id": "视频ID",
    "model_version": "模型版本",
    "timestamp": "2024-01-01T12:00:00Z",
    "is_reference": false,
    "confidence": 0.85,
    "source": "analysis",
    "similarity": 0.92,
    "matched_cat_id": "匹配的猫咪ID"
  }
}
```

### 3. 用户隔离

- 每个用户拥有独立的Qdrant集合：`cat_features_{user_id}`
- 用户之间的向量数据完全隔离
- 支持多租户架构

## 配置选项

### 环境变量配置

```bash
# 启用影子模式（必需）
export SHADOW_MODE_ENABLED=true

# 启用特征存储（默认：true）
export SHADOW_STORE_FEATURES=true

# 存储阈值：只存储相似度高于此值的特征（默认：0.0）
export SHADOW_STORE_THRESHOLD=0.0

# 每只猫最多存储的特征数量（默认：1000）
export SHADOW_MAX_FEATURES_PER_CAT=1000

# Qdrant连接配置
export QDRANT_HOST=localhost
export QDRANT_PORT=6333
export QDRANT_API_KEY=your_api_key
```

### 配置文件

在 `config/config.yaml` 中：

```yaml
shadow_mode:
  enabled: true
  store_features: true
  store_threshold: 0.0
  max_features_per_cat: 1000
```

## 适应性学习机制

### 1. 持续学习

- **每次识别都学习**：系统会存储每次识别的特征向量
- **渐进式改进**：随着数据积累，识别准确性逐步提升
- **演变适应**：能够适应猫咪外观的自然变化

### 2. 特征管理

- **阈值过滤**：只存储高质量的特征向量
- **数量限制**：防止单只猫咪的特征数据过度膨胀
- **时间戳记录**：支持基于时间的特征分析

### 3. 新猫发现

- **未知特征检测**：识别可能的新猫咪
- **渐进式确认**：通过多次观察确认新猫身份
- **自动标记**：为新发现的猫咪生成临时标识

## 监控和调试

### 1. 日志监控

系统会记录以下关键日志：

```
Storing features for cat_id: c_12345, similarity: 0.8500, user_id: user_123
Feature storage disabled by configuration
Warning: failed to store features: connection error
```

### 2. 配置检查

使用配置检查脚本：

```bash
./scripts/check_vector_config.sh
```

### 3. 测试脚本

使用测试脚本验证向量存储：

```bash
python3 scripts/test_vector_storage.py
```

## 性能优化

### 1. 存储策略

- **智能过滤**：只存储有价值的特征向量
- **批量操作**：优化Qdrant写入性能
- **异步处理**：不阻塞主要识别流程

### 2. 内存管理

- **特征数量限制**：防止内存过度使用
- **定期清理**：清理过期或低质量特征
- **压缩存储**：优化向量存储格式

## 故障排除

### 常见问题

1. **特征未存储**
   - 检查 `SHADOW_MODE_ENABLED` 是否为 `true`
   - 检查 `SHADOW_STORE_FEATURES` 是否为 `true`
   - 检查Qdrant连接配置

2. **存储失败**
   - 检查Qdrant服务状态
   - 检查API密钥配置
   - 查看caby_ai日志

3. **性能问题**
   - 调整 `SHADOW_STORE_THRESHOLD` 提高存储阈值
   - 减少 `SHADOW_MAX_FEATURES_PER_CAT` 限制特征数量
   - 检查Qdrant资源使用情况

### 调试命令

```bash
# 检查配置
./scripts/check_vector_config.sh

# 查看日志
docker logs caby_ai

# 测试向量存储
python3 scripts/test_vector_storage.py
```

## 最佳实践

1. **合理设置阈值**：根据实际需求调整存储阈值
2. **监控存储量**：定期检查向量数据库大小
3. **备份数据**：定期备份Qdrant数据
4. **性能测试**：在生产环境前进行充分测试

## 总结

caby_ai的向量存储机制确保系统能够：

- ✅ **自动学习**：每次识别都会存储新的特征向量
- ✅ **适应演变**：随着猫咪外观变化而调整识别模型
- ✅ **用户隔离**：每个用户的数据独立存储
- ✅ **性能优化**：通过配置选项平衡准确性和性能
- ✅ **故障恢复**：具备完善的错误处理和日志记录

这个机制使得系统能够持续改进，为用户提供越来越准确的猫咪识别服务。
