---
type: "always_apply"
---

common
三个子项目（backend_server, caby_ai, caby_vision）都通过其目录下的
- scripts/quick_deploy.sh进行构建
- scripts/logs.sh进行日志查看
- scripts/test.py进行功能测试
不要维护其他代码用于构建、查看日志和测试。

backend_server
- backend_server是后端服务程序，可以本地编译测试，但不可以本地部署测试。所有涉及到backend_server的测试均有用户手动完成。
- backend_server里有docs/backend_api.md，每次新增或者修改API之后需要更新它。
- backend_server维护三个docker images, 分别是backend服务器，mysql和minio,它们通过docker-composer来统一维护。

caby_ai
- caby_ai是AI分析服务程序，可以本地编译测试，也可以本地部署自动测试。
- caby_ai维护两个docker images，分别是caby_ai和qdrant数据库。

caby_vision
- caby_vision是负责视觉的服务程序，可以本地编译测试，也可以本地部署测试，支持CPU和GPU两种模式。
- 它是基于tritonserver进行构建，所以每一个新增模型都需要兼容CPU和GPU两种模式运行。
- 集成新模型的时候，需要确保代码保持最简，不要拷贝没有必要的冗余代码。